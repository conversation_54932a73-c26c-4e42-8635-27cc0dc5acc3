{"__meta": {"id": "X780f284168cb387fb8e1593756f5abf6", "datetime": "2025-08-02 16:37:39", "utime": **********.529031, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152656.639948, "end": **********.529089, "duration": 2.889141082763672, "duration_str": "2.89s", "measures": [{"label": "Booting", "start": 1754152656.639948, "relative_start": 0, "end": **********.354036, "relative_end": **********.354036, "duration": 2.714088201522827, "duration_str": "2.71s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.354069, "relative_start": 2.***************, "end": **********.529099, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZELMsW4Z1uFwCvwNxCYXu9TobmPX1s86xD4QATxL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1351667820 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1351667820\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-88568852 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-88568852\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1119982690 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1119982690\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1398894739 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398894739\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-451986140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-451986140\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1690698619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:37:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IngzZndSc1pJejNEQUo5YXRqb0xlcEE9PSIsInZhbHVlIjoiSGFmSmRsWnVVMDhkZld2QlBLSVdrS3psaENsVjc3RXhsYVNob0JBTmFIWUhJS3hLanF6WGlJVFNvc1pTZlQvbW9KeUxXcmw1UmhPODU0cHJmNW9PVTQybjc3cStaV3VxNWw3ckJHUkoraVhxVjdrejNsWTlpKzZrbmFDTzdFVnNweFh1b1BLaHI5cXhTVy9YamRJdmJKeDlPVzN6RHJqL1JOUUxES1lYMlk3RFg5RTZ5UEhhT0ZHZ1lMb1RDamh0aGp4M0pROU4vVlF5NWxhWFpyak5QOUhXTm9IMW9sLzV3S1d1NUdHS0VPVjlmOUp6NVpSK2JScVViSk1ZZnNMSW1ZYm1vZWZNUUhFU3daRWlHYnZrcklSdVN1QWQzTGVCVk5VaVMzVTB5cHBFTEdjQzJGaklDb3RKUWxlYmVnMFlFMzh2T1VHY3RML3JyWXFzSytkdmtMdENTMnUzOUdMVERlOHhEdXlxZzBycjNKclo0Y0w4MngzUmZpRWVScU5QRUFGbFZGa3VEVXFkNXFDSDR4R3dkTTNod0N1RitkOFBjOXhsMUUzaTNZU0pJaHBsOHJvZnNsaWlTdHNSMWtxR0IxKzVJZ1g2MlVZWDFOSms2REtoakZ6aW1QQmlRYkJ4U1U0Zkd3bUFnSFJGcGh1YndJSVUyUXFreEQ2cGVIa1AiLCJtYWMiOiIwM2I5MzgwMDY4ZWQwM2ViNzVhNDFiNTI0OTA0YjI3YmQ5Mjc2OGU1N2VkMTMxNjgzNzZkNTU1YzcwNTZjZTU0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik01YkdySGhJV012K0JGbnQ4ZDRpU1E9PSIsInZhbHVlIjoiYkFBZUxvTHZTcGcvZUg2VEtWVDFJRUxpbllmZlZsbnlWd3hNaERQb3lYZXZraFV3VnVZS0tWdHluVHZ6dlU0Uk9XUEM1K1dFOTR3bWtsWWlxMkRuT1B2b0pBdWE5K2ZmMkEraGM1em5oS0tBbmk1MWl5SGY5U1FrUTRLa1hmd1RqUklDY3N3RUt1SHQrQTN0S1dON01IK09GN3k1a2lEQ0JoSk9FcS9LdFVaMWJYMWtwTVNPakxydnNnNlRmcjlrMVhseVV4TjRzU3lsMkVIWkJDbjBjQUdCdU82bWpMSTlpV2Z2MFR2RXJTMS85SG9HM0o2Si9DdVY0U1FxUzYveDFXUzR3MHpubVN5RTZrRHA5dkF6dVJkT2tPVjNGMlNYeitjYkFVN0FjYU1CeDVLMytyT0gzbzhiTzZSUlVvM1lOM3cwM2Z3MkJvRzdOWXNvVTNvR2pGL1N5OXhCTDI1SEEyYnVnelFpTmJFV25ERnBmZlEwbGdPd3pndzFpaWhVTzlyUjBqdHpkY1cyZVRQZVY1QTN4em5IVzJ6R0J5UHdGNHFaMmU1dU0xc0JIY25TK25TY1ZZMTAyOFNOMldkL1k3TEJXY2l1TjlTK0hUUFJ2cS9LNG5kMUFWL1J5MmlSSnMrV250dWJvWTVFVnE0MG9EbzM1RnBIcGpnV0dBNXAiLCJtYWMiOiJlODc0YjQ5ZmMwMGEyM2MwM2FhNWIyN2JjM2MwNDljZDNmMmI0NjUzMTAxODRmOWQ4NDIxZDgxMTgzNWE1OTNiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IngzZndSc1pJejNEQUo5YXRqb0xlcEE9PSIsInZhbHVlIjoiSGFmSmRsWnVVMDhkZld2QlBLSVdrS3psaENsVjc3RXhsYVNob0JBTmFIWUhJS3hLanF6WGlJVFNvc1pTZlQvbW9KeUxXcmw1UmhPODU0cHJmNW9PVTQybjc3cStaV3VxNWw3ckJHUkoraVhxVjdrejNsWTlpKzZrbmFDTzdFVnNweFh1b1BLaHI5cXhTVy9YamRJdmJKeDlPVzN6RHJqL1JOUUxES1lYMlk3RFg5RTZ5UEhhT0ZHZ1lMb1RDamh0aGp4M0pROU4vVlF5NWxhWFpyak5QOUhXTm9IMW9sLzV3S1d1NUdHS0VPVjlmOUp6NVpSK2JScVViSk1ZZnNMSW1ZYm1vZWZNUUhFU3daRWlHYnZrcklSdVN1QWQzTGVCVk5VaVMzVTB5cHBFTEdjQzJGaklDb3RKUWxlYmVnMFlFMzh2T1VHY3RML3JyWXFzSytkdmtMdENTMnUzOUdMVERlOHhEdXlxZzBycjNKclo0Y0w4MngzUmZpRWVScU5QRUFGbFZGa3VEVXFkNXFDSDR4R3dkTTNod0N1RitkOFBjOXhsMUUzaTNZU0pJaHBsOHJvZnNsaWlTdHNSMWtxR0IxKzVJZ1g2MlVZWDFOSms2REtoakZ6aW1QQmlRYkJ4U1U0Zkd3bUFnSFJGcGh1YndJSVUyUXFreEQ2cGVIa1AiLCJtYWMiOiIwM2I5MzgwMDY4ZWQwM2ViNzVhNDFiNTI0OTA0YjI3YmQ5Mjc2OGU1N2VkMTMxNjgzNzZkNTU1YzcwNTZjZTU0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik01YkdySGhJV012K0JGbnQ4ZDRpU1E9PSIsInZhbHVlIjoiYkFBZUxvTHZTcGcvZUg2VEtWVDFJRUxpbllmZlZsbnlWd3hNaERQb3lYZXZraFV3VnVZS0tWdHluVHZ6dlU0Uk9XUEM1K1dFOTR3bWtsWWlxMkRuT1B2b0pBdWE5K2ZmMkEraGM1em5oS0tBbmk1MWl5SGY5U1FrUTRLa1hmd1RqUklDY3N3RUt1SHQrQTN0S1dON01IK09GN3k1a2lEQ0JoSk9FcS9LdFVaMWJYMWtwTVNPakxydnNnNlRmcjlrMVhseVV4TjRzU3lsMkVIWkJDbjBjQUdCdU82bWpMSTlpV2Z2MFR2RXJTMS85SG9HM0o2Si9DdVY0U1FxUzYveDFXUzR3MHpubVN5RTZrRHA5dkF6dVJkT2tPVjNGMlNYeitjYkFVN0FjYU1CeDVLMytyT0gzbzhiTzZSUlVvM1lOM3cwM2Z3MkJvRzdOWXNvVTNvR2pGL1N5OXhCTDI1SEEyYnVnelFpTmJFV25ERnBmZlEwbGdPd3pndzFpaWhVTzlyUjBqdHpkY1cyZVRQZVY1QTN4em5IVzJ6R0J5UHdGNHFaMmU1dU0xc0JIY25TK25TY1ZZMTAyOFNOMldkL1k3TEJXY2l1TjlTK0hUUFJ2cS9LNG5kMUFWL1J5MmlSSnMrV250dWJvWTVFVnE0MG9EbzM1RnBIcGpnV0dBNXAiLCJtYWMiOiJlODc0YjQ5ZmMwMGEyM2MwM2FhNWIyN2JjM2MwNDljZDNmMmI0NjUzMTAxODRmOWQ4NDIxZDgxMTgzNWE1OTNiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690698619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZELMsW4Z1uFwCvwNxCYXu9TobmPX1s86xD4QATxL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}