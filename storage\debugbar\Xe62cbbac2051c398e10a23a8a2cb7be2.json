{"__meta": {"id": "Xe62cbbac2051c398e10a23a8a2cb7be2", "datetime": "2025-08-02 16:42:22", "utime": **********.053162, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152938.801837, "end": **********.053225, "duration": 3.2513880729675293, "duration_str": "3.25s", "measures": [{"label": "Booting", "start": 1754152938.801837, "relative_start": 0, "end": 1754152941.855005, "relative_end": 1754152941.855005, "duration": 3.0531680583953857, "duration_str": "3.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152941.855052, "relative_start": 3.****************, "end": **********.05323, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hlcQ4xJko2cVW5aruQYjJYRd73Ne4b4BqV2cGsHO", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-79165651 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-79165651\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-799418340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-799418340\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-541839988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-541839988\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-231587379 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231587379\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-272800440 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-272800440\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-373540495 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:42:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9wQ05Jc0FPc0l4bnphbG5sb2V6enc9PSIsInZhbHVlIjoiZmlzcGNsd1d2ZzNOZ0JldUo4OXVNbnZoTXNjZWtxbXV0Z3BJN3dycm5wUnZnOFNyU0xOcFlJNkQzbTRCcmZJL2xpdVhWT0FWd0Q1Zjc3TU9GYkJOVjczcEgvS1hwempzdWxJYS9KNWJZREVmUTBralpUZ0tKSzNGRmdTWU1XeDlNSk95c3Mxc2t5b3BHL2dTaG9PYXlmcnJUU0tiUTMzS2E2ZEpmbXZQWDZYMkhtWGtxOHVTMXVnSFY3UEV5MHQ2UnkxbzAwdkV1dkd1dXkwREJrL0FYK2h1YkIvZk1DREphREZ6VFNEZ0NTS3ZPbEx2S2ljWitCdmZsUjJ2cWJ4djNJR1A3bnlFdStoTmd6K2lkMGU3NjZPLzRGLzJlNnJTSWVDNEF5RUU0cVVkanhWOXZRNEg3dm1NSGZvM05hU3FWMUwxbHRFVGhYNnNqTkFtUVJrWGdkV3B2SHBPam5aRG1OQVN0aGk3YUxRQkxuY2s1NU90eXVlTC9QcTBEUTVtWEdVZU9nKzRqa1dJQUVIWUU1TG5uZGN3MVpEK2ZsMXk5dzVCM2h5SVZ1aVBBSG4xNFlDV09QL3NMVGNtajdtNHQrdzN3RU5ncllsMFF2NTNJVm1yemVud3RwR0p1SDNydGZJYTNuM3VaMFlKRHhadmJOQjBCK2ZvSUs5cTVyUGoiLCJtYWMiOiJhMGY0YjhiODNmMDJhMDJjMGQzM2ZmZjgzNDE4MDk3NWJhOTg5MzI4NTc5MTVjY2NhZDlmNDM3ZGIwOWZmZDAxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:42:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkF0UWpzOUxMd0tEWk9tUkJsS3pZeXc9PSIsInZhbHVlIjoibHIrMXFyemF3Y3dKUzRTSFhLcGlhU0dJMG1uYjFzZ0RVSXE1c2tFSDVCSDFHbm0zYmpTOTZtNTZmaDVjdm5VYzUwNmJ1bzBFeXZ4ZnpxZVlNVS93VnVoOGVwNUVNZDBvaVFia1FvNFNtV24vekNpWHYvbEplRExIMTVHeDRQVkdwTUJ1eUVhYmtWb016ZUMzcnZDR0NvUHhXdlUwOEN3YzNBU2lOdmVkRzRaMVNhcVJvT1J3YldNd0tMRDllYnJuWEJQc083YWZjWTdLLzROL1dWbmVOUWxHR1dXTHFsMGZ1R1hGczRlZWxodFQ2VkNYcFJsdmVPUnBhcDBjSnVhZHdYcG5XWkZmbHVWMUgzdjdOL3dCdTNQOVBzZkQvQXVzT1pZNUIzd2NGekZHNDZ6bXk3eHo3WEREeFQ3OGoraVAwcU1SNnBuYWZxam9qeW4rNUtDOFNPVStzOWpWRGF6akg5Tkpmc0xKMU1jTG1rL2VxTy9XYnNKdDJtbWVRaUE2T1cra2VabHhYS3RGZlIyamtQNG1pYUt3NVJQMUVNWFBOSVZsellGRDlRcGJPTnVFZVUycEphNkc4QlJmc0g5YUhKUFJDeS9oc1V0VTNkZ1BVNGtvSFBOeGdDS2V6Z2d0RXZCUDNaSGM1MEMrVGFhSk4yOTJGSzg3QmFkUmVqQW8iLCJtYWMiOiI5NmZmMmMzZjgxYTlkNTI0MjE4MjY5NWM5MTgwNjM3ZDFhNGRmNThhY2E0OWI0YzM2YTMzYTQ5YWYxNTA5MmEwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:42:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9wQ05Jc0FPc0l4bnphbG5sb2V6enc9PSIsInZhbHVlIjoiZmlzcGNsd1d2ZzNOZ0JldUo4OXVNbnZoTXNjZWtxbXV0Z3BJN3dycm5wUnZnOFNyU0xOcFlJNkQzbTRCcmZJL2xpdVhWT0FWd0Q1Zjc3TU9GYkJOVjczcEgvS1hwempzdWxJYS9KNWJZREVmUTBralpUZ0tKSzNGRmdTWU1XeDlNSk95c3Mxc2t5b3BHL2dTaG9PYXlmcnJUU0tiUTMzS2E2ZEpmbXZQWDZYMkhtWGtxOHVTMXVnSFY3UEV5MHQ2UnkxbzAwdkV1dkd1dXkwREJrL0FYK2h1YkIvZk1DREphREZ6VFNEZ0NTS3ZPbEx2S2ljWitCdmZsUjJ2cWJ4djNJR1A3bnlFdStoTmd6K2lkMGU3NjZPLzRGLzJlNnJTSWVDNEF5RUU0cVVkanhWOXZRNEg3dm1NSGZvM05hU3FWMUwxbHRFVGhYNnNqTkFtUVJrWGdkV3B2SHBPam5aRG1OQVN0aGk3YUxRQkxuY2s1NU90eXVlTC9QcTBEUTVtWEdVZU9nKzRqa1dJQUVIWUU1TG5uZGN3MVpEK2ZsMXk5dzVCM2h5SVZ1aVBBSG4xNFlDV09QL3NMVGNtajdtNHQrdzN3RU5ncllsMFF2NTNJVm1yemVud3RwR0p1SDNydGZJYTNuM3VaMFlKRHhadmJOQjBCK2ZvSUs5cTVyUGoiLCJtYWMiOiJhMGY0YjhiODNmMDJhMDJjMGQzM2ZmZjgzNDE4MDk3NWJhOTg5MzI4NTc5MTVjY2NhZDlmNDM3ZGIwOWZmZDAxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:42:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkF0UWpzOUxMd0tEWk9tUkJsS3pZeXc9PSIsInZhbHVlIjoibHIrMXFyemF3Y3dKUzRTSFhLcGlhU0dJMG1uYjFzZ0RVSXE1c2tFSDVCSDFHbm0zYmpTOTZtNTZmaDVjdm5VYzUwNmJ1bzBFeXZ4ZnpxZVlNVS93VnVoOGVwNUVNZDBvaVFia1FvNFNtV24vekNpWHYvbEplRExIMTVHeDRQVkdwTUJ1eUVhYmtWb016ZUMzcnZDR0NvUHhXdlUwOEN3YzNBU2lOdmVkRzRaMVNhcVJvT1J3YldNd0tMRDllYnJuWEJQc083YWZjWTdLLzROL1dWbmVOUWxHR1dXTHFsMGZ1R1hGczRlZWxodFQ2VkNYcFJsdmVPUnBhcDBjSnVhZHdYcG5XWkZmbHVWMUgzdjdOL3dCdTNQOVBzZkQvQXVzT1pZNUIzd2NGekZHNDZ6bXk3eHo3WEREeFQ3OGoraVAwcU1SNnBuYWZxam9qeW4rNUtDOFNPVStzOWpWRGF6akg5Tkpmc0xKMU1jTG1rL2VxTy9XYnNKdDJtbWVRaUE2T1cra2VabHhYS3RGZlIyamtQNG1pYUt3NVJQMUVNWFBOSVZsellGRDlRcGJPTnVFZVUycEphNkc4QlJmc0g5YUhKUFJDeS9oc1V0VTNkZ1BVNGtvSFBOeGdDS2V6Z2d0RXZCUDNaSGM1MEMrVGFhSk4yOTJGSzg3QmFkUmVqQW8iLCJtYWMiOiI5NmZmMmMzZjgxYTlkNTI0MjE4MjY5NWM5MTgwNjM3ZDFhNGRmNThhY2E0OWI0YzM2YTMzYTQ5YWYxNTA5MmEwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:42:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373540495\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-416631642 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hlcQ4xJko2cVW5aruQYjJYRd73Ne4b4BqV2cGsHO</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416631642\", {\"maxDepth\":0})</script>\n"}}