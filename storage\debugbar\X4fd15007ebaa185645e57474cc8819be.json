{"__meta": {"id": "X4fd15007ebaa185645e57474cc8819be", "datetime": "2025-08-02 16:38:38", "utime": **********.882405, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152715.83553, "end": **********.88246, "duration": 3.0469300746917725, "duration_str": "3.05s", "measures": [{"label": "Booting", "start": 1754152715.83553, "relative_start": 0, "end": **********.664541, "relative_end": **********.664541, "duration": 2.8290109634399414, "duration_str": "2.83s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.664599, "relative_start": 2.***************, "end": **********.88247, "relative_end": 9.775161743164062e-06, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "whctGhcCoZt8VGY5DxhpOWAT9RkP5XP53DHOV764", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-122607757 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-122607757\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2016140217 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2016140217\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1139397124 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1139397124\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-903636237 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903636237\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1195247136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1195247136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1718597864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImkrUXZDTlY0ZldJN3hoS1VHMHdnZXc9PSIsInZhbHVlIjoieWxxa1pGTGdBTm9ha2pldVpnTGNqY1hzTFN0YUhvQ3RSYkJWMFlHZlpOUjdrNXpKcHZZWDhLWUVtbkd0R2NrUXpzY1p5Nk54ZUEwYUQ1TDQwU2h3ZG5KckhCaDdJUzV4bDhJRDZmeWNId1JwZGM3MDJPbGR0ZnZOTGtDdnJZUXVjL3loSjQyb2VUdFJTb1o2bDJEZ255Y05DMzBJRVREelNGTjJWUFY0aWJHS0JUdGhCNmhpK24vQUFWa3BUMUFQYjNVWUtaNWVnS2RVenBtd09HYzhQVkZ3eER0VlUvandiYUZJTHUvbmovY0Vtem5UQ3I4SVdHNmkvN1cvVHEzVjNRQytlVEFQWG5XVHg3THE3WkVycnI2aUhYUDFQQkQ3SFdKa2hXcm1wZVNyeDRSWE93b1BPMHlLOHhtN2FScnZmL0tYZVFycXRnRHhNbVpVMGR5TlkzY1hPcG1xb2NKalRIYjg3MlBQMDR3bk5aSjRod05oZlNhTEFDL2V1RlpDZnlkZDF2UERsRTVKV2t6RVJuQ3FvcEhTUzRMbFc2a3dwWlBIcFlpZDB6ZlBYemFPSU9VVnJ6NnZDbTZBOTcwL1FtU2ZVRWl1SEVKUVgrTzU4UUpwUFVjOURlN0pGUTBsMHlVMnViNXNhTVAwZkFCZ25PclFyVlV0Qnk5NkJQRVAiLCJtYWMiOiJmZmQ5YzQ0NjkyNGFiZDVmY2U5ZWRhYzEwNmRjOTQ3Y2NkZjdiZDE5MGZlNDlmMWRkOTg4MWEzZmM2ZmJmMzg5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjRIVjBFTmg2eTZNL2FhQk5KMHh2NGc9PSIsInZhbHVlIjoicFcrK09rU3VMYzhMaE95SCs3aEFhOTA2bEpWbkd6QU9GNnFJMFJhM0orb0xNeXpaQ3FCbjU0WmZraytHUHJ5bkI0eDQ2VW5qaVVsa0JyS2hoSnJVdmlPWXJJY2lqRDU4Zko5RkRyYVk1VTFJVFVwQndYUDZiVVFWdjhXWGk3WGhFYjdwb1cxZlVUVlZjS0lydFVuSEZKeDIxUjVzYTlsbmJhR09heFJ0bVdtN2pJZGRXRFhZYnF5RE0rNlpKK09nQ29KbjA0aVdJSE9UbjJqbXpyMnEzcTd2WjMzbWFUUWdiRHNGOXRieXloSHFITS84cTU2WnUrdUtXei9SalZ3SFhrOW4yTEIzYTM5QW00TWg2V0xXUFluQlJhTjBzb1VTd25xSm95ekROSHIwNjVSQzZWVDRNV0NoNEZ0aCthcVlCb1V5alBFTHY4OVZVQVNQUnZJWlkrT2hoR2dKMmhrRFVGQUpZSjVpUW1nckRnQ0k5dFlXU3ViV2YwZVlEcmtGNHI5Q1FJb3kwN1dWaEpZTkJFL3BYeldyMXAyWUtUaWYrcTFGaGE0L3VHWjQ4bEdkb1FLbi93NW5HdStNY1NDM1NFcUM5UXU2UWdoYzdKRitybjlVczd1SThpK202TGVKRGFxbkRneG9QakhCdW5OcDFGcTliL2tBc1lwSWZEdXEiLCJtYWMiOiI2ODU1MTYxZDllZWMyNTZkY2Y5NGI3ZTk5MmRhOTZiMGRlZjJkMTU1NTE3MTM4ODA2MzE2MDg0ZjFiZmYxYzJmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImkrUXZDTlY0ZldJN3hoS1VHMHdnZXc9PSIsInZhbHVlIjoieWxxa1pGTGdBTm9ha2pldVpnTGNqY1hzTFN0YUhvQ3RSYkJWMFlHZlpOUjdrNXpKcHZZWDhLWUVtbkd0R2NrUXpzY1p5Nk54ZUEwYUQ1TDQwU2h3ZG5KckhCaDdJUzV4bDhJRDZmeWNId1JwZGM3MDJPbGR0ZnZOTGtDdnJZUXVjL3loSjQyb2VUdFJTb1o2bDJEZ255Y05DMzBJRVREelNGTjJWUFY0aWJHS0JUdGhCNmhpK24vQUFWa3BUMUFQYjNVWUtaNWVnS2RVenBtd09HYzhQVkZ3eER0VlUvandiYUZJTHUvbmovY0Vtem5UQ3I4SVdHNmkvN1cvVHEzVjNRQytlVEFQWG5XVHg3THE3WkVycnI2aUhYUDFQQkQ3SFdKa2hXcm1wZVNyeDRSWE93b1BPMHlLOHhtN2FScnZmL0tYZVFycXRnRHhNbVpVMGR5TlkzY1hPcG1xb2NKalRIYjg3MlBQMDR3bk5aSjRod05oZlNhTEFDL2V1RlpDZnlkZDF2UERsRTVKV2t6RVJuQ3FvcEhTUzRMbFc2a3dwWlBIcFlpZDB6ZlBYemFPSU9VVnJ6NnZDbTZBOTcwL1FtU2ZVRWl1SEVKUVgrTzU4UUpwUFVjOURlN0pGUTBsMHlVMnViNXNhTVAwZkFCZ25PclFyVlV0Qnk5NkJQRVAiLCJtYWMiOiJmZmQ5YzQ0NjkyNGFiZDVmY2U5ZWRhYzEwNmRjOTQ3Y2NkZjdiZDE5MGZlNDlmMWRkOTg4MWEzZmM2ZmJmMzg5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjRIVjBFTmg2eTZNL2FhQk5KMHh2NGc9PSIsInZhbHVlIjoicFcrK09rU3VMYzhMaE95SCs3aEFhOTA2bEpWbkd6QU9GNnFJMFJhM0orb0xNeXpaQ3FCbjU0WmZraytHUHJ5bkI0eDQ2VW5qaVVsa0JyS2hoSnJVdmlPWXJJY2lqRDU4Zko5RkRyYVk1VTFJVFVwQndYUDZiVVFWdjhXWGk3WGhFYjdwb1cxZlVUVlZjS0lydFVuSEZKeDIxUjVzYTlsbmJhR09heFJ0bVdtN2pJZGRXRFhZYnF5RE0rNlpKK09nQ29KbjA0aVdJSE9UbjJqbXpyMnEzcTd2WjMzbWFUUWdiRHNGOXRieXloSHFITS84cTU2WnUrdUtXei9SalZ3SFhrOW4yTEIzYTM5QW00TWg2V0xXUFluQlJhTjBzb1VTd25xSm95ekROSHIwNjVSQzZWVDRNV0NoNEZ0aCthcVlCb1V5alBFTHY4OVZVQVNQUnZJWlkrT2hoR2dKMmhrRFVGQUpZSjVpUW1nckRnQ0k5dFlXU3ViV2YwZVlEcmtGNHI5Q1FJb3kwN1dWaEpZTkJFL3BYeldyMXAyWUtUaWYrcTFGaGE0L3VHWjQ4bEdkb1FLbi93NW5HdStNY1NDM1NFcUM5UXU2UWdoYzdKRitybjlVczd1SThpK202TGVKRGFxbkRneG9QakhCdW5OcDFGcTliL2tBc1lwSWZEdXEiLCJtYWMiOiI2ODU1MTYxZDllZWMyNTZkY2Y5NGI3ZTk5MmRhOTZiMGRlZjJkMTU1NTE3MTM4ODA2MzE2MDg0ZjFiZmYxYzJmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1718597864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-452844801 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">whctGhcCoZt8VGY5DxhpOWAT9RkP5XP53DHOV764</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452844801\", {\"maxDepth\":0})</script>\n"}}