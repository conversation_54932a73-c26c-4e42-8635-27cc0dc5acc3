{"__meta": {"id": "Xc3c36d88e5e48a5c6bd7bcde1091f125", "datetime": "2025-08-02 16:38:42", "utime": **********.231537, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152718.923205, "end": **********.231622, "duration": 3.3084170818328857, "duration_str": "3.31s", "measures": [{"label": "Booting", "start": 1754152718.923205, "relative_start": 0, "end": 1754152721.967548, "relative_end": 1754152721.967548, "duration": 3.0443429946899414, "duration_str": "3.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152721.967603, "relative_start": 3.***************, "end": **********.231632, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "264ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "whxRvhddflhK7Y3imtc8U1NInlCC6KXxFbOcQ15y", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-1094972134 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1094972134\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1529326924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529326924\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-377882104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-377882104\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-139162668 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139162668\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-10606418 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-10606418\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-419317317 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNzN3NPZTZwUFlpenpId3NIdVpZZWc9PSIsInZhbHVlIjoiSVIybDhPOWs0KzMvUFo5YjZ1NURYK010N3pLcndsRmk4TW5BcHdncHVOa1lndURyOXJvclJLRStEeStwa3FuaXNTOUdDd3kzZzlhZnRCbFpDWnFBS05HaU9PNXRFUkZabUxDVkUySWxST04wSGIweWNIWk9XQTJkdFc0cGhwS2RCWWJmNUVqb2gzcTl4TGhINm8zZmRyRUpPSmlZRCs5b1RkREJMNEdDaVIyNjdtZ3NGcDJmazVtSWlJSEdDZS9jWWVmcVF3QUtyYm94WTFPbEtqY0JjRnJDMTA1RC9DVlFDbmk5ODV0ZnEyR0xDRjJtRnIzQ09nZHhRUURoNDg5TmJQR0RjRzkzRUJkNU0vaWVtYmg1QWpOTUpMNFFjNGx6NVZFcUthK0dNNkI3WU1sVWNISUZmWkFVc1lDbktKaTFLZ3Q3QVBUVzFvenlVcWR0b0R0dFQyYUQ0aGtlL3VJOEZvSFBab1FTNitvd1FPUlNQcDM5ZC9JOVQzcEFiNFRrRzhqaVc0NDVwRmJ1UlFobXRqdXkvc2t1ak8waUJYV1ZJYVhMaVpiSitIdHFjZFcyekZQVXlxazVaNlRhdmovaHZCOG81b0phT2VIWHZLMSs5cEFpTXdCbnhYTEpaeDQ0Z2lDZXUzNy8wM3ZSYVpUaWRkS1hYeWE3eWxCaTh4TFYiLCJtYWMiOiI5OGNlNTQ1MmIwODI0ZTk2ZDY0ZDlmYjFjYzU4ZTIwYWYwNTBlY2UxNGNkNjhhYWZiNTBkMDgxN2RjNmYwODA4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpFZmpIZ2ZoSTVTWHQ3ZHJiUGVGaVE9PSIsInZhbHVlIjoiaWJ3dWp6V3B4VFYyenBROExsMVh0QUFMV3VuSUZwMXFrUk42Y3NDcmZRdk9JNHRYVitHemVEZVB1Z2VPWEQyeWFzYmJYWnZvN1ZqN2pSMHpuSlQrcjAwU0hvZkMrb0ROaTZERXlpNkFTWUJRRmthNmVqYVJUUThHanc4QmNKSVRaSzBrUCt4dmx5Z0N0TEJ1MDl1aVVPOE5BYTlIR3lSdHN1eGVueFRwU0d6RVhBdlJsWG1ENkNSY0J3c0s0T20wd1pCcWo0cTJhOFNmVzI1b3AxM3ZSbGlIZCtxSmRaYkNRWEs5MlFiYU1kYVViK3VCQVVNTzVPU0NuRnlHTC9lWHRXMUp1WmZvZG04L2krK2RUREdSZ3l2YkYyQ2hxcWVmTW5aK1hNNFdiMDRHV2YyQkFIek93VjZlYjNiM0xHSjVXTE9VS0ZFVWFPbDZPWVRHY0d6b2xUZWhHaW1rN3JrUS9VUmh6S0Rwb1RDVG9OV05pdjE2bWlVdEVLZExXWFVKU3VNWHpkWkt1K1oyTEc3ZkF3SnFzdzBsSlhROElkamVrSjlSVHA2MXcrZnprWit3WUJKQ1BLNTlVamI0eUJqV2k5ckptMU05SkZSWWpmUHdBMG5CVXJwVkNjV2pEUEFUSUJUQzhTK2ZobGdOZTQvTENYSTlUcUZBK2FReGJ4OWQiLCJtYWMiOiIzNjNhOGMxMmJlNzFlMDMzMzI1N2EzOWRhMDk0ZDJjMjljZGE3MDEzOTc5OGFlMjJkZTIwYzQxYjY2NWZjOTJmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNzN3NPZTZwUFlpenpId3NIdVpZZWc9PSIsInZhbHVlIjoiSVIybDhPOWs0KzMvUFo5YjZ1NURYK010N3pLcndsRmk4TW5BcHdncHVOa1lndURyOXJvclJLRStEeStwa3FuaXNTOUdDd3kzZzlhZnRCbFpDWnFBS05HaU9PNXRFUkZabUxDVkUySWxST04wSGIweWNIWk9XQTJkdFc0cGhwS2RCWWJmNUVqb2gzcTl4TGhINm8zZmRyRUpPSmlZRCs5b1RkREJMNEdDaVIyNjdtZ3NGcDJmazVtSWlJSEdDZS9jWWVmcVF3QUtyYm94WTFPbEtqY0JjRnJDMTA1RC9DVlFDbmk5ODV0ZnEyR0xDRjJtRnIzQ09nZHhRUURoNDg5TmJQR0RjRzkzRUJkNU0vaWVtYmg1QWpOTUpMNFFjNGx6NVZFcUthK0dNNkI3WU1sVWNISUZmWkFVc1lDbktKaTFLZ3Q3QVBUVzFvenlVcWR0b0R0dFQyYUQ0aGtlL3VJOEZvSFBab1FTNitvd1FPUlNQcDM5ZC9JOVQzcEFiNFRrRzhqaVc0NDVwRmJ1UlFobXRqdXkvc2t1ak8waUJYV1ZJYVhMaVpiSitIdHFjZFcyekZQVXlxazVaNlRhdmovaHZCOG81b0phT2VIWHZLMSs5cEFpTXdCbnhYTEpaeDQ0Z2lDZXUzNy8wM3ZSYVpUaWRkS1hYeWE3eWxCaTh4TFYiLCJtYWMiOiI5OGNlNTQ1MmIwODI0ZTk2ZDY0ZDlmYjFjYzU4ZTIwYWYwNTBlY2UxNGNkNjhhYWZiNTBkMDgxN2RjNmYwODA4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpFZmpIZ2ZoSTVTWHQ3ZHJiUGVGaVE9PSIsInZhbHVlIjoiaWJ3dWp6V3B4VFYyenBROExsMVh0QUFMV3VuSUZwMXFrUk42Y3NDcmZRdk9JNHRYVitHemVEZVB1Z2VPWEQyeWFzYmJYWnZvN1ZqN2pSMHpuSlQrcjAwU0hvZkMrb0ROaTZERXlpNkFTWUJRRmthNmVqYVJUUThHanc4QmNKSVRaSzBrUCt4dmx5Z0N0TEJ1MDl1aVVPOE5BYTlIR3lSdHN1eGVueFRwU0d6RVhBdlJsWG1ENkNSY0J3c0s0T20wd1pCcWo0cTJhOFNmVzI1b3AxM3ZSbGlIZCtxSmRaYkNRWEs5MlFiYU1kYVViK3VCQVVNTzVPU0NuRnlHTC9lWHRXMUp1WmZvZG04L2krK2RUREdSZ3l2YkYyQ2hxcWVmTW5aK1hNNFdiMDRHV2YyQkFIek93VjZlYjNiM0xHSjVXTE9VS0ZFVWFPbDZPWVRHY0d6b2xUZWhHaW1rN3JrUS9VUmh6S0Rwb1RDVG9OV05pdjE2bWlVdEVLZExXWFVKU3VNWHpkWkt1K1oyTEc3ZkF3SnFzdzBsSlhROElkamVrSjlSVHA2MXcrZnprWit3WUJKQ1BLNTlVamI0eUJqV2k5ckptMU05SkZSWWpmUHdBMG5CVXJwVkNjV2pEUEFUSUJUQzhTK2ZobGdOZTQvTENYSTlUcUZBK2FReGJ4OWQiLCJtYWMiOiIzNjNhOGMxMmJlNzFlMDMzMzI1N2EzOWRhMDk0ZDJjMjljZGE3MDEzOTc5OGFlMjJkZTIwYzQxYjY2NWZjOTJmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419317317\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-738784674 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">whxRvhddflhK7Y3imtc8U1NInlCC6KXxFbOcQ15y</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738784674\", {\"maxDepth\":0})</script>\n"}}