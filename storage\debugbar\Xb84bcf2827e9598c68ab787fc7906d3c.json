{"__meta": {"id": "Xb84bcf2827e9598c68ab787fc7906d3c", "datetime": "2025-08-02 16:36:36", "utime": **********.178684, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:36:36] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.170027, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754152593.677582, "end": **********.178743, "duration": 2.5011608600616455, "duration_str": "2.5s", "measures": [{"label": "Booting", "start": 1754152593.677582, "relative_start": 0, "end": **********.856133, "relative_end": **********.856133, "duration": 2.178550958633423, "duration_str": "2.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.856171, "relative_start": 2.1785888671875, "end": **********.178749, "relative_end": 6.198883056640625e-06, "duration": 0.32257819175720215, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46137056, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02689, "accumulated_duration_str": "26.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.996702, "duration": 0.02296, "duration_str": "22.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.385}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.0643752, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.385, "width_percent": 5.69}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.118261, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 91.075, "width_percent": 3.83}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1282501, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 94.905, "width_percent": 5.095}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-163453694 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-163453694\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1316568029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1316568029\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-744327823 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744327823\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1788745196 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZOanVKWDFjOWRqaDZBTkplMGN2UGc9PSIsInZhbHVlIjoiY1h1aXdhOXJaQkdncDZHQXJ0SS9HTC85enNLalB4Vm9KUC8yTXlCUjlNaHM5RUhQVUxTRTZoYWl0TDNBNjB2YXo0cW1EbXdWWmYyZXZVRm85UEhLL0NqalhEMlhtd3JtUkRhRm1TRHY5U25JMS9ZK2dLU25sWDhVUUthS1kySmtqdFVCZndPVUcrV3pmaDZ6OE9McnpRSWlmK1VKM01UM0hYWjViSld1NE9XMHJxajBuYTY2UnFlT2RKZklYRE1hbmRiZHhBWkdyTW9vOTVaYUk1VFdwTnNSeFRtc3RreVV3QkRNQ280Z2ZmODZiRW90VjByaGhGVlVVS040T24xWjh3Ui93T21uYnR5bGNiejFqallhOE9FZjVRb05YYTZCbjM3WUZOS0JLVzRTZWZzR1hmdXc4azJqTXU1dVNMK3dRbWx4NFBYeUxpT1JmNm1tcVVHU3VoVnlWVzZQLzBsZ0tyWWdRM2h1aGdlVVgrMlBQTkxsTVIvMWlLRm14aXg4Zzk3ZmxnMlR5SkEweHVJa1dMQ2RyaThQTGhOTXNYWEg1dGxSQ2ZzMUNOaHkxa1pFNEpzYnl1U3Q5OU9mSEd4WkEwRGdzSTB5bjNmcmtXSGQwR3VKK3oyTnpqN2RVbCtiNzZRbkhjU0tUNUFqWWlRTXhDWkljWXUvUlo2M2lXekciLCJtYWMiOiJlZmI1MzFkNzg2ODk4YmZhYjY2ZjY4OTUzNWIwNDZiYWI4ODgwOTY2MWM3YzY4ZGZkNWY0YWVkZjkwMTIyZjQ5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InYwQmdtaWN5cXlsQnFnbi9mK1pLSHc9PSIsInZhbHVlIjoiVTR1M3VWRi82am9HdXIzUGtXU1dSblVva3U2ZkxHTGVSdHZGN0lOeVYvUFNHc2laZk1jYXQ5UDl5SSsxVXpFbThONGRaakhwdngrZThIL0FLblg4QUJUUllpZGtGRE8wR2ZZUmJHMVloRjBCVVplaS9LaVg5dzkxUGF3aFhpcmhGME84NHljVmxCUHN6YTNCMHlWSnBFNExpd2FRUzlwRVVGQjFHZEZvSHI5VmlRVStXazQxMHgwMFNIbE92UDlGTFZNQTFsaVFqWEZ4d2xPTERROWpVT0dUcjNjcENVZHlTU0tWTWhYb1VRY2dTRDJ2b3Fsc1BEcmRIWEh6MmlPMzBXSGQybkhXblBrVzQ4K2QxZlNuWW8zQ2JwRGQyMkhoM0RQNDNBcGFTby9yQ3N3Umh5bHRVaW9GUi9lT2ZUOVNKb1JOQXRDNDZpZWFyOWxtY0creUxJV09tR1lXeUJTSERRNmpCeGNveFhGVFJkVTMxWHR0a0RQL2cva3VtQ2RxOHA1aXVHazVvZThsVzB5aGwvcS9lVjkzbHR5WXhkMEk0aUhxYlhsU2xrRmlJQk11Y04rZDdlNmJSNHplMzNtNGM3Q3RWTksyVTNwYU1VTjFvRzZwNVlIQW1BdGpUQ25FSFJEdDBzY0NkMGdGeGpSOHI0Szc3amlWYzhSMmc3UGkiLCJtYWMiOiI5MjJmN2QxODYyOWZmNDEyMGQyYWE4Mjg4YzdkMDdkZDViZmFjYWYxNjU5MDM3YzM1ZmVjYzViNzEzZjE0NzRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788745196\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-982466183 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qX0CnOObck67LLGXwnxE1rbA703TvCmAFmaCZvwn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982466183\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2089288830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:36:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjN6NkFHMnYvbW9TRE4xSCt1R1JQVWc9PSIsInZhbHVlIjoiZG9CUUF4Y01pU0g4QlN2L3JOZElISi92Ylh4cXBnbkR3a0JONHRSUExzQ1hHby92OG1vNW9OMkRkalRmNTV6cGRmSWlqU2tiSUcwR0w0OG9vOElDK21UeGxISTN5aFl2RHNiY2xXZTBQZENmUWlhaTdzdEZuNEorQVVocEErOXFHOVZ0empscWdocTVHMUNhalBLUHZWbWMyOGM0Q1NZYis5UVliRmUyQVNIS3dMc2lGUnc3VjZIcjk5TkVaYXJIWGFOWmZiVXYyWUcreFN0TWlFTzVjNHp6M1A2cDE3T2lRS0I2bjY2Rk5IeFB3UEdSQVM2MUxEZFp6VmFFZFJBWWZQQlNVWmQxR0haMHRoSitWOW5wL3d6R0hsbkY1b0lKN2lCMjNzYnZzTHNBNCtqOTRVdVdzRTZkQU9jTlRoNW9GcWpTelk0M2hRZEErTUpjT2R0WGJVcnpmWi9uczBqSVNzd1BNa1JXaW8zaHFSNXlJdDRxZHBhOUVCZkg0cVpDWENTV3RVbHlHL241RFZESlBCTXBocTkyM29GWEdac0k2UzdDQmwxOFpUN0RVNFYzS1QvQjZUejZOMTFxb1paUEx1K0JTYmVoSktIUm9rQWZrQUt2RjZua2JtcG4xQkhZZFh0OSt1WnkrZUNuL09lQUhwTCtFRS9LL0IrUldXbUUiLCJtYWMiOiI3ZGY2MzlkZTk3MjU2MDZiZGFjMDc1ODU3NWUzYmFmMzYyZjFhNjJhNTBiN2Y3YjRjOGUwMmI2NTcwODAyNWFhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:36:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InYyU1B6QlU2WlB6N1AyVXRGK09sV0E9PSIsInZhbHVlIjoiUGg2dkZsdXk2bkd2UVk5WG9mYktPSmhEY2FCV0U2QytLSkVobS9PRzd4RnpaZjUrKzE0eWJmQ0JpME90MUhHRUNpdzZ2RzdoRUowRURwYmR6SDNWOWMwNHo2SFRXdVdHK3BDeExZMlZ5VWxDTUFhY0pITHZIUUxCdGF0d3JFdkZtdnlRY2FGUHU3TmNrUm9LekhpUFNhNzA4R3YvaE5uSDlsTkpjZjFkVVRGNURWaUhnYW9UTmxFcCtKZEVyREJaTlkyZFJSRm9wdm51WVZPR3F6SkZKdTA5Mkszdllud3podzBaQXpYSTZTbVF3QTdmbTNxRFRiaGFQOEtnWitzT3RzeTlMMUVxQ1lwdWFLQUh2UWh4ZzMrV1V2bnhCaUh5UmllZkVsR3d5c1dWb090S0tzUmFMUFV6L2dGQ1h6alcrNElEVUNBSHk1MWorN2NEam9ULytpNDdSdDNRUDhXZU5qcVlZd3ltVmxKRFlNSkJqdWdEK2pzekhGamhTTVpGd3FxZWVuKzZPcWFHbXl0amo1YTBKeVdhVVNoY2hUUUt2UW1QU0psdm5VZGRVa2p0bGZmd0I3YlVORk9HSGpZOWdkdWJVQk9pU1Fqa3lPWjcxZEplbHFvQzVnZzh4ejVoMkowWm12ZjJYdEZEb083UldVR1cyUGlMUDQxWDZoZGkiLCJtYWMiOiIzNmY0NGRlOGY4YjFiMTM2ZjE3NWYyM2JkMzgyYWEzNWZhMWY3OTkyYmU1ZmI1YzVkYTUwYTg0Nzc3NzhmNmI1IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:36:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjN6NkFHMnYvbW9TRE4xSCt1R1JQVWc9PSIsInZhbHVlIjoiZG9CUUF4Y01pU0g4QlN2L3JOZElISi92Ylh4cXBnbkR3a0JONHRSUExzQ1hHby92OG1vNW9OMkRkalRmNTV6cGRmSWlqU2tiSUcwR0w0OG9vOElDK21UeGxISTN5aFl2RHNiY2xXZTBQZENmUWlhaTdzdEZuNEorQVVocEErOXFHOVZ0empscWdocTVHMUNhalBLUHZWbWMyOGM0Q1NZYis5UVliRmUyQVNIS3dMc2lGUnc3VjZIcjk5TkVaYXJIWGFOWmZiVXYyWUcreFN0TWlFTzVjNHp6M1A2cDE3T2lRS0I2bjY2Rk5IeFB3UEdSQVM2MUxEZFp6VmFFZFJBWWZQQlNVWmQxR0haMHRoSitWOW5wL3d6R0hsbkY1b0lKN2lCMjNzYnZzTHNBNCtqOTRVdVdzRTZkQU9jTlRoNW9GcWpTelk0M2hRZEErTUpjT2R0WGJVcnpmWi9uczBqSVNzd1BNa1JXaW8zaHFSNXlJdDRxZHBhOUVCZkg0cVpDWENTV3RVbHlHL241RFZESlBCTXBocTkyM29GWEdac0k2UzdDQmwxOFpUN0RVNFYzS1QvQjZUejZOMTFxb1paUEx1K0JTYmVoSktIUm9rQWZrQUt2RjZua2JtcG4xQkhZZFh0OSt1WnkrZUNuL09lQUhwTCtFRS9LL0IrUldXbUUiLCJtYWMiOiI3ZGY2MzlkZTk3MjU2MDZiZGFjMDc1ODU3NWUzYmFmMzYyZjFhNjJhNTBiN2Y3YjRjOGUwMmI2NTcwODAyNWFhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:36:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InYyU1B6QlU2WlB6N1AyVXRGK09sV0E9PSIsInZhbHVlIjoiUGg2dkZsdXk2bkd2UVk5WG9mYktPSmhEY2FCV0U2QytLSkVobS9PRzd4RnpaZjUrKzE0eWJmQ0JpME90MUhHRUNpdzZ2RzdoRUowRURwYmR6SDNWOWMwNHo2SFRXdVdHK3BDeExZMlZ5VWxDTUFhY0pITHZIUUxCdGF0d3JFdkZtdnlRY2FGUHU3TmNrUm9LekhpUFNhNzA4R3YvaE5uSDlsTkpjZjFkVVRGNURWaUhnYW9UTmxFcCtKZEVyREJaTlkyZFJSRm9wdm51WVZPR3F6SkZKdTA5Mkszdllud3podzBaQXpYSTZTbVF3QTdmbTNxRFRiaGFQOEtnWitzT3RzeTlMMUVxQ1lwdWFLQUh2UWh4ZzMrV1V2bnhCaUh5UmllZkVsR3d5c1dWb090S0tzUmFMUFV6L2dGQ1h6alcrNElEVUNBSHk1MWorN2NEam9ULytpNDdSdDNRUDhXZU5qcVlZd3ltVmxKRFlNSkJqdWdEK2pzekhGamhTTVpGd3FxZWVuKzZPcWFHbXl0amo1YTBKeVdhVVNoY2hUUUt2UW1QU0psdm5VZGRVa2p0bGZmd0I3YlVORk9HSGpZOWdkdWJVQk9pU1Fqa3lPWjcxZEplbHFvQzVnZzh4ejVoMkowWm12ZjJYdEZEb083UldVR1cyUGlMUDQxWDZoZGkiLCJtYWMiOiIzNmY0NGRlOGY4YjFiMTM2ZjE3NWYyM2JkMzgyYWEzNWZhMWY3OTkyYmU1ZmI1YzVkYTUwYTg0Nzc3NzhmNmI1IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:36:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089288830\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-734915046 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734915046\", {\"maxDepth\":0})</script>\n"}}