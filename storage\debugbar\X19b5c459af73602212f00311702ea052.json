{"__meta": {"id": "X19b5c459af73602212f00311702ea052", "datetime": "2025-08-02 16:38:44", "utime": **********.700321, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152722.300655, "end": **********.700375, "duration": 2.3997201919555664, "duration_str": "2.4s", "measures": [{"label": "Booting", "start": 1754152722.300655, "relative_start": 0, "end": **********.504681, "relative_end": **********.504681, "duration": 2.204026222229004, "duration_str": "2.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.504745, "relative_start": 2.***************, "end": **********.700381, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TKVwminp5WvW6X3dvsXPHfdbq09igk3igtQkdbXl", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-39368706 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-39368706\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-111766006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111766006\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1513980383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1513980383\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1486699575 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486699575\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-231122704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-231122704\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-963188763 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMwb2RXdk1WY04xM2pvRGJXcE9KTEE9PSIsInZhbHVlIjoiRHdoS3pkTUxsb1QxUmdLelF2VGlrVDZ3NUJieWxBcS9hVGNNNXZjeFpkTkxSMFl5N3NWbC9YcUZHWHgxZ1dOS3BXNGN0aGhDVG5CM2J3ZGgvS1FDK24xNldSVVV4aUM5SjRGdkpqZGwvNEdNRGFTVnFkQmphMER2VEI3ckxyeFZIaWZ5NTdvcG1hUXpyRW1rQzdwS09zSTc5VVNXcDdFQUlEanJZY0VRcTMzY0RxVmZZalc1SjJnM3pvS3ZxS0k5cmovU25IR0F0eTFoTU1uR3FUeElIREVadTNxaUVvQWNORjF2cDh3d0JNOEM5bjhlb21mcC9vMTB2UEg1SldIOS9uMWVaeEVlY3FsQzFzN1lPeGs5VlNQaGNEQ2c2a0VSQXlLT1Z1Q1ZRUmV1S282ODJ6aHpIN3NYTmVMb0Q1dzdMem4vb1dja3pXYlNBK0pCWGZJT2F6dkVOWDRCRkFNYzdwdWJSQ1EyeGVGdHpCYWZ1YVEvdU9NZlB6R1ZRNzd3b0NpYmcxY2h0bkZlRWc2ZGsyTmlGRVlURnltTlI5TDVuSHZZQ2p5THF1ajB4OXozVUxFbE1xOS9WMStOZ3JFS3IvRzNNWVdCWWQxaXN5OUJBZkVubkdwMGN1VjdOMVpsZThMOWN3cWo3RXI3aGNZSUY1ZmgzbWczQW1VR0JVQ2oiLCJtYWMiOiJiYjQwMWRmNmE0ZDhhNTMwYjc0YmNhMGM2OTYyNTkxNWEwNGY1Nzg4YTc0MTg5MjIzNWZiZTA3MmE1NjFmZTBlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1WVndIc2gzbC9BS2FXemhPT2QrQlE9PSIsInZhbHVlIjoib3V1bzFOYnVwNVlMaDdodnNpZnBwSCtOZXZTZ1BJYzl3Rm1uc0xYNVlpYVpIL2JraUxndTFHRTBib0s1WkE1Y1dzTmFraWl6Z0k2S1czcitxRzR5OWlNb1BYbWFIcmJrYkdFKzNJWmtIc3liaFd6TWZOQzRZUnZVbVlwRmx4UTNBc2FLM2xzazFHS2UzbjdpYmxlSVg5RHdrT3hDL2dGajJkL3V5d293ZVBwNzY2QkpWMXZSR2ZXa1poaDRmTFZpTElDS3A3NXA3ajZYVEF1MXdpMkp0RUxPZm5xMVFSdmt1djV0TTQxTlFXUEh0WTVOTTdkT3FvYWVZZXdONHlvSUhXdW9JdzUvaDNkODdISVRYeUJmRHhtdzcwQ0VteTlJclcyM2l6dS84WWFsWjdkT2dnY3dLZHhYOEk0bUhmQmQ5VTN4M0srb1lQRGEvdHBUelVWQ21GUVQrazVvdkJUaHBRN0xDVmF0YUlhUEVKQklHY0hmNTZYZFVQUjNrUXpQUUdxZysvZ05pektOa1JVbGxISWtWNTVkZGhTZjZGc0F4ZGJGWStIc0FIamt5ODc0K1Z4cVYwMmtWZWhSL21jYlYyemZCclBIeGdsUmtjWldha3AzZkYzdXVvWFNBVzdCVHBORkZGakRZNFNtcmZWQUpkQjlVcDBQcEdOOVIzK0EiLCJtYWMiOiIxNmRjOWM2NGYzZTAzNjY4MDg1OWJkMjhjZjRhNzE0M2ViZWQ1Zjk1MWRhMGJhN2YxMDkwOWIzOWQ3ZTVhMGM5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMwb2RXdk1WY04xM2pvRGJXcE9KTEE9PSIsInZhbHVlIjoiRHdoS3pkTUxsb1QxUmdLelF2VGlrVDZ3NUJieWxBcS9hVGNNNXZjeFpkTkxSMFl5N3NWbC9YcUZHWHgxZ1dOS3BXNGN0aGhDVG5CM2J3ZGgvS1FDK24xNldSVVV4aUM5SjRGdkpqZGwvNEdNRGFTVnFkQmphMER2VEI3ckxyeFZIaWZ5NTdvcG1hUXpyRW1rQzdwS09zSTc5VVNXcDdFQUlEanJZY0VRcTMzY0RxVmZZalc1SjJnM3pvS3ZxS0k5cmovU25IR0F0eTFoTU1uR3FUeElIREVadTNxaUVvQWNORjF2cDh3d0JNOEM5bjhlb21mcC9vMTB2UEg1SldIOS9uMWVaeEVlY3FsQzFzN1lPeGs5VlNQaGNEQ2c2a0VSQXlLT1Z1Q1ZRUmV1S282ODJ6aHpIN3NYTmVMb0Q1dzdMem4vb1dja3pXYlNBK0pCWGZJT2F6dkVOWDRCRkFNYzdwdWJSQ1EyeGVGdHpCYWZ1YVEvdU9NZlB6R1ZRNzd3b0NpYmcxY2h0bkZlRWc2ZGsyTmlGRVlURnltTlI5TDVuSHZZQ2p5THF1ajB4OXozVUxFbE1xOS9WMStOZ3JFS3IvRzNNWVdCWWQxaXN5OUJBZkVubkdwMGN1VjdOMVpsZThMOWN3cWo3RXI3aGNZSUY1ZmgzbWczQW1VR0JVQ2oiLCJtYWMiOiJiYjQwMWRmNmE0ZDhhNTMwYjc0YmNhMGM2OTYyNTkxNWEwNGY1Nzg4YTc0MTg5MjIzNWZiZTA3MmE1NjFmZTBlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1WVndIc2gzbC9BS2FXemhPT2QrQlE9PSIsInZhbHVlIjoib3V1bzFOYnVwNVlMaDdodnNpZnBwSCtOZXZTZ1BJYzl3Rm1uc0xYNVlpYVpIL2JraUxndTFHRTBib0s1WkE1Y1dzTmFraWl6Z0k2S1czcitxRzR5OWlNb1BYbWFIcmJrYkdFKzNJWmtIc3liaFd6TWZOQzRZUnZVbVlwRmx4UTNBc2FLM2xzazFHS2UzbjdpYmxlSVg5RHdrT3hDL2dGajJkL3V5d293ZVBwNzY2QkpWMXZSR2ZXa1poaDRmTFZpTElDS3A3NXA3ajZYVEF1MXdpMkp0RUxPZm5xMVFSdmt1djV0TTQxTlFXUEh0WTVOTTdkT3FvYWVZZXdONHlvSUhXdW9JdzUvaDNkODdISVRYeUJmRHhtdzcwQ0VteTlJclcyM2l6dS84WWFsWjdkT2dnY3dLZHhYOEk0bUhmQmQ5VTN4M0srb1lQRGEvdHBUelVWQ21GUVQrazVvdkJUaHBRN0xDVmF0YUlhUEVKQklHY0hmNTZYZFVQUjNrUXpQUUdxZysvZ05pektOa1JVbGxISWtWNTVkZGhTZjZGc0F4ZGJGWStIc0FIamt5ODc0K1Z4cVYwMmtWZWhSL21jYlYyemZCclBIeGdsUmtjWldha3AzZkYzdXVvWFNBVzdCVHBORkZGakRZNFNtcmZWQUpkQjlVcDBQcEdOOVIzK0EiLCJtYWMiOiIxNmRjOWM2NGYzZTAzNjY4MDg1OWJkMjhjZjRhNzE0M2ViZWQ1Zjk1MWRhMGJhN2YxMDkwOWIzOWQ3ZTVhMGM5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963188763\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1434364525 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TKVwminp5WvW6X3dvsXPHfdbq09igk3igtQkdbXl</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434364525\", {\"maxDepth\":0})</script>\n"}}