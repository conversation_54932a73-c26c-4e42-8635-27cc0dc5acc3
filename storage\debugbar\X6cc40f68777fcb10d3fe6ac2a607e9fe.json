{"__meta": {"id": "X6cc40f68777fcb10d3fe6ac2a607e9fe", "datetime": "2025-08-02 16:38:47", "utime": **********.164813, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152724.744395, "end": **********.164874, "duration": 2.4204790592193604, "duration_str": "2.42s", "measures": [{"label": "Booting", "start": 1754152724.744395, "relative_start": 0, "end": 1754152726.976873, "relative_end": 1754152726.976873, "duration": 2.232477903366089, "duration_str": "2.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152726.976907, "relative_start": 2.****************, "end": **********.16488, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDSjMUtKuxqEH7EqzNGkWcis3rXoKAVuTvNWtNzx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-1861931866 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1861931866\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-948094735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-948094735\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2020899567 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2020899567\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-613979038 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613979038\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1041793957 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1041793957\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2104094377 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1CK3Q2d2UvZmppbHAyUlhEMlZ2d0E9PSIsInZhbHVlIjoiSkhaRVNUSURodTR2VDg2NlhIZTJpemtzM1ZPaUlQalpyVjVBRzYyam9zL2lGRUdzZjFhQUUrOHUwdW9EWXhmMHR1bXRCakZnZFBaTVBtU2k2SWdUQnpqdDY3bHJKc3dUWnk2S0E2TGJVMmRWMzZ3eUZiRXJQM29IdGZGNk9USTRZY3pIZ2JXVEl2MVlwT0swdURpaDVSbURUS25TK2V5Z2g4MGQyRjlwWm5od3NLQzVLcFo5ck1WWjMzUHA0QWJSdGRybUx4TnJrUGExOVFzZVN3WTVPU3haKzc1aVRMMUJlc2xxMFkzd3k5WUgxVXhheGxNOElqenVxMGN0OTFDRFN1WkNyZ0ZhcS9TVm1sVUdzKzYraFppNm9vc0g0dm5KVERKV2ZLdUhaVWVaam54d1QrU2w5VktiRitBZ0RmbWdWUEd0RzN3MU1jbVV1SlpTQTNNd2NTbUsxaFBHK3pYS1ZtUWdhMmZHU1FnejM1eWxRdnBhWUMzdDJVWGhPTC90QWlrc21iaGhtbEY3TTAzaHoraXc5WUFrNmpsNDBkMFp0eDA2RmN4S3lJeldYdGFyc0Y2WDdrNDZhNlBmOE1sc09lRGR5QjYyOUNZM2poMzQzNlk5UjJ4UVo4amc3WWk2Tks2SHhmWk52VVN6S2Z1WEM2c0RuWnU1QnE5OXU3Uk4iLCJtYWMiOiIyYzdlNzIyYjM2N2ExY2M4NmUwNzkwYjA5MjMxMDQyMGRhNDM5NWZiOTNiODk5ZjM0Y2ZkYWQ3MjlmNzdkMjhhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik5KeVVjNHlqa2pLVHIxSWN2cjYwWFE9PSIsInZhbHVlIjoiZm9rcEpWc2lnS2tGYmlaSFk5UG9ZR2xmZ2t3SEtwaG1rVzQ0WmgwOWR6eUxWd2dwYXlNNzYvZWhkMjQxa2FYZHJVTDUxWFJQalNpeEcvVFgzWjE5dWhDam9nMHY0QzkvZGhmekJkaHNCYmIvaGFmZ01IblB1MVQ5MVg5SlMrektVaE8zVEZ3VmZwMTdBSmpZNmM0QzJKRjdPQmN2NGZpUzE2QUdMM3B3Y0YxR3Y3UzVLOHlxQmV1SWhDaUdlTSt1VVdnYWpIeHVyMGVJUjh4S0FuWEp5UE5PTm11U1RPelh5cW5tZlpiNGVrelYwYldsaitUYzBSem93RitRVG1SZU9STUJkN1NqOFNjUW9KakIzdE9SSTl0WWhueXNWbXFkZlNpdGhEb0dtcU5PQlRsWVdDeWNWT0ZYamRjOWpuMzRDZVhteEgxRWV0WTRYdG1RTEZxYkx5b2RRQTJhMGlsS0FRb0ZJSFRpUXNnUUpHN1dMYjdpN0ZNd3dYM0lWbXpDODF4S1RNQUhieFBoNWIwbG9ZK1ZNVzRFbkJDWGx0RUV1MVhBQS90QnFSVUREeDc4QkFOTmlvNDQzR2pOK3BIcytYY0FEQmdVM2UzaDNyK0ZjYmczR0xXWEZYTWIwSU9peVZNWnhETitzT0kxbDdjdkpWcXZvdGF5WlpSM3R3RjciLCJtYWMiOiJjMjFiMjQ2MGVjM2Y4MDU1NmI1ZWE3MGViMjY3MjM2OTdiNzFmZGVjOGQ0MjJmZjU3ZGM1MTAyNzViOTk2NzEyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1CK3Q2d2UvZmppbHAyUlhEMlZ2d0E9PSIsInZhbHVlIjoiSkhaRVNUSURodTR2VDg2NlhIZTJpemtzM1ZPaUlQalpyVjVBRzYyam9zL2lGRUdzZjFhQUUrOHUwdW9EWXhmMHR1bXRCakZnZFBaTVBtU2k2SWdUQnpqdDY3bHJKc3dUWnk2S0E2TGJVMmRWMzZ3eUZiRXJQM29IdGZGNk9USTRZY3pIZ2JXVEl2MVlwT0swdURpaDVSbURUS25TK2V5Z2g4MGQyRjlwWm5od3NLQzVLcFo5ck1WWjMzUHA0QWJSdGRybUx4TnJrUGExOVFzZVN3WTVPU3haKzc1aVRMMUJlc2xxMFkzd3k5WUgxVXhheGxNOElqenVxMGN0OTFDRFN1WkNyZ0ZhcS9TVm1sVUdzKzYraFppNm9vc0g0dm5KVERKV2ZLdUhaVWVaam54d1QrU2w5VktiRitBZ0RmbWdWUEd0RzN3MU1jbVV1SlpTQTNNd2NTbUsxaFBHK3pYS1ZtUWdhMmZHU1FnejM1eWxRdnBhWUMzdDJVWGhPTC90QWlrc21iaGhtbEY3TTAzaHoraXc5WUFrNmpsNDBkMFp0eDA2RmN4S3lJeldYdGFyc0Y2WDdrNDZhNlBmOE1sc09lRGR5QjYyOUNZM2poMzQzNlk5UjJ4UVo4amc3WWk2Tks2SHhmWk52VVN6S2Z1WEM2c0RuWnU1QnE5OXU3Uk4iLCJtYWMiOiIyYzdlNzIyYjM2N2ExY2M4NmUwNzkwYjA5MjMxMDQyMGRhNDM5NWZiOTNiODk5ZjM0Y2ZkYWQ3MjlmNzdkMjhhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik5KeVVjNHlqa2pLVHIxSWN2cjYwWFE9PSIsInZhbHVlIjoiZm9rcEpWc2lnS2tGYmlaSFk5UG9ZR2xmZ2t3SEtwaG1rVzQ0WmgwOWR6eUxWd2dwYXlNNzYvZWhkMjQxa2FYZHJVTDUxWFJQalNpeEcvVFgzWjE5dWhDam9nMHY0QzkvZGhmekJkaHNCYmIvaGFmZ01IblB1MVQ5MVg5SlMrektVaE8zVEZ3VmZwMTdBSmpZNmM0QzJKRjdPQmN2NGZpUzE2QUdMM3B3Y0YxR3Y3UzVLOHlxQmV1SWhDaUdlTSt1VVdnYWpIeHVyMGVJUjh4S0FuWEp5UE5PTm11U1RPelh5cW5tZlpiNGVrelYwYldsaitUYzBSem93RitRVG1SZU9STUJkN1NqOFNjUW9KakIzdE9SSTl0WWhueXNWbXFkZlNpdGhEb0dtcU5PQlRsWVdDeWNWT0ZYamRjOWpuMzRDZVhteEgxRWV0WTRYdG1RTEZxYkx5b2RRQTJhMGlsS0FRb0ZJSFRpUXNnUUpHN1dMYjdpN0ZNd3dYM0lWbXpDODF4S1RNQUhieFBoNWIwbG9ZK1ZNVzRFbkJDWGx0RUV1MVhBQS90QnFSVUREeDc4QkFOTmlvNDQzR2pOK3BIcytYY0FEQmdVM2UzaDNyK0ZjYmczR0xXWEZYTWIwSU9peVZNWnhETitzT0kxbDdjdkpWcXZvdGF5WlpSM3R3RjciLCJtYWMiOiJjMjFiMjQ2MGVjM2Y4MDU1NmI1ZWE3MGViMjY3MjM2OTdiNzFmZGVjOGQ0MjJmZjU3ZGM1MTAyNzViOTk2NzEyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104094377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2036273460 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDSjMUtKuxqEH7EqzNGkWcis3rXoKAVuTvNWtNzx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036273460\", {\"maxDepth\":0})</script>\n"}}