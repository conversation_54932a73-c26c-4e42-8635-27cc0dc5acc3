{"__meta": {"id": "Xfeb0bff2c1dcc9ece940a312200085f7", "datetime": "2025-08-02 16:42:39", "utime": **********.360151, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152956.197076, "end": **********.360207, "duration": 3.16313099861145, "duration_str": "3.16s", "measures": [{"label": "Booting", "start": 1754152956.197076, "relative_start": 0, "end": **********.056734, "relative_end": **********.056734, "duration": 2.8596580028533936, "duration_str": "2.86s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.056771, "relative_start": 2.8596949577331543, "end": **********.360211, "relative_end": 3.814697265625e-06, "duration": 0.3034398555755615, "duration_str": "303ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47495792, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=955\" onclick=\"\">app/Http/Controllers/FinanceController.php:955-1029</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02718, "accumulated_duration_str": "27.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.250548, "duration": 0.0229, "duration_str": "22.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 84.253}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.306323, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 84.253, "width_percent": 5.04}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 958}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3160841, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 89.294, "width_percent": 10.706}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-84003078 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-84003078\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1745215993 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1745215993\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1064337484 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1064337484\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFjM3FWQUVFV0puM0w5QkIzb2pJMkE9PSIsInZhbHVlIjoicGM2c1IrdzB4V3pTcUNzeU9nbTlubHkrNlYvVzJoVGNaNWJ6UkpYa1dJb1hSY2lqUWpkUitJYjN2dUlRRlNJaTJjbW5QM1lEclpEOEEyTWZjaWlZdFFTakUvNmVkRDEyZGc5SjNHeGpzZ3pISDRZMTI4WEJIeTlLSzdWNDBTYWthazF0cVJSeHg1VWV2b29xR0l2WjVlNVR3NDRBRzJkOWVwWmRQcmVBTWxGTXZuYUZNV0o1OHRFb0VWWWlxVlA1ZUR2V2ZzdmYzNjRCaUhaVUVmRDBLY2FIcUdXTnlZZUJNeXVtVm5vcUZsZXpjQmFyM1NFNVpvZDBncFBUMlEwTjBVTktIamJwemQxMzFoL1pwKzZIT3I4RkoxN0laWkYwMUVWYW1vYldldlRWdkNqMlNkY0NDU1E2cXZzNVdEcnU5MVZGSGVLUEdMNDVqL1hhYXZBVk5LZzhiVU1PQlJUVm0zWkNYSEdyRVJOSzlaWXJSME1OY3hib0FlZHEzaDJ3UHQ5TTNxQWdmY2JRSytMSW9TbkN1R2Ric2RrelFmZStiQXZ6bVNpZ2RmQm53bE04Y0FQb1NSRCtFMHFjbmRVVGF4S1BFWVMvUTdrWEVVdXBjWVVCNCtRNU05UVRnQmVhNlNRM0NvZXYzWVFiSVpTZ3Ryc2plTW1KbzllaXk0MngiLCJtYWMiOiJmMzkxZTIxYzQ5NzkzMGFiNmEyY2FiMmQ0YWFjYjExODdlMWE5YjlmYjA4ZjAxNjM2MmZiYmM5NDJiMTI1NmQwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImE4VmpqR2lTUmh0SGFMK3V6ZDN3Mmc9PSIsInZhbHVlIjoiZFlYWmVhSUt6R2R5Q3FicnhNaStNZmN1eCtKc2NNMkhJazhhNEZQVytHRkxuOHNPdnFLWVZTdlFidUtNWGNPVW1UVHdBbzcwQzYvQlU5cmZ4SzdQUmR6ZmRWZ2tXbHNQYXN4bmNPMWhrYzhQQ1l4QWlkK3lWRUVWWEFueVJmVllsWEkycHhKZmwrM3BzZ2pIS2xKRVkwelljcXU5cWVhWXNvSlplanZOdlJpbVpBY1dhQWRQRE9NdEVUakI2Zk04NVBtUmJVYkNaVUFPWEhqTEZPaHhTeDUzNEU2VkdScVorMmhoelR5OFNrQXBydlllUG9oQkxQdDB1b2V6K3k0VmZGUlFLcGdWbDh4TmQzWit3Z0wzU0N6dGowejdVMEE3RzAwdk9qZ3NnVmhhdFdVYXlBZ2xSUDNhRCttK0JZOVBOTXZ0Q3dCa1NVU2MvYW5CVGRTMS9kUUE4azlGNXRaNUNNa29EQW9DYW0yNU5FZnR0K3pEcHFsT21DY3h2dlBOeGpud0dQU2lCaEIxMEtLL2VrcmdKSjRZV2p3MnZPVFdPZCtnQXVuNEZ6amNiNlpvTldHZ3JMYmQ5eVI3OHlxcU44RE1mdTlNVm1lQkJ4RGVGVXoxV0tXaTJaRFBNYWd3Y3BBZXdYSkFYS0ppemlnR25jclNYOEVmQWpYODN2WEsiLCJtYWMiOiJhODMzZjNiMjVmYTQ2YzExOTZjMDAzMTcwM2I4NTNhODA4ODI5MDE2YTQ1NzkxMWQyYjg0NmJiNjhjYTRlNmQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1090944151 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">86XiLSfEoHmOjEpXQOTVS9FkVDNCSFGADRy0KhTS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090944151\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-703132685 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:42:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBoZzc5dUFyWFlFczM5ZTJib0NvUVE9PSIsInZhbHVlIjoiTVo1azM2SUxHdzFNNVRDL3N4NkxKc1FIZlRIY2lxaVg4aDFzR00yTXpsM2VQMGdseUIwdnRyb0Z2VktodnJWaC9SUktNeGtDWm5xbldSVkRubUZtRWNLMVg1dXRRTE94ZkpXNkRIMEo2ODRRMndRckN6dm9OMk5oblhhcTl6WHNPcE9jaUZmZEREMzQ5aTdMV3MvemNJeUZiRWp2T2RUeStwaVBnYVIyMjd0MGozd2VlZ2pKdHZ1K3puS284ellSNERJd1pYY3A4eHdpL1FPMWhmM0tYYlFGSEQ0dDFmZjB2dC9zY0M0cHFjcEpKUGxjSU5SWVdqSXdNQnBvdGd2OWdpcTFDUkxSM2RFekd6UFBZY0NnYy9ZaEtVTEcrQjJldTRidjNYSFdXZTN1RDJERnNqaVVJOE0yNnhqaW52SVZRd1FEcFFOY3Z4QXhxeWJFNnl5eWJiN3ZqUnpVTkVMZHFiMmNocEw3azRGaG8vRi9Wa2dXQUdsTG9DZlYyT0NCT0xvcXFMSk0zb2s1Wkk4RGlNVUppRWVqUnlIdVdubjFSV0I1bUtPZDhkdWhoZXhldHI4UkJST0NwaW1kWVRaNXpsZTRLYUUxUW9PUUkxSFFGU21VK0pyVER1aUtiTTlyeisrM2tyTndUcWRLejRDMVZoWkt2OGhDQkVLOEQrM1kiLCJtYWMiOiI0NTUyOTM5YjQ4NjJlYzA2OWQ4N2E0YzFmZjYzY2EyZTk4YjE0ODAzZjNhMDc5YTAyNzU4YzAzZGY4OGE1ZTNiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:42:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVrMzRMcUhsRkFXZ1g0cXJnN0RUOUE9PSIsInZhbHVlIjoiQko0RHNZelhQS0ZmQzlIMG94MllpbUtoNnBWUFFkY3o1V09GbkNEdHBJSGwvNXlObWdzQzNMdHVQVEZwVVF0REY1RDlqMDRWSUFSdGhIY0xsdWZhNnVMcEpyaDhTclY3TkEvM1U5dW5nSzR1eWxFRnFHZGd4K2FOTGYwQXFWWEtJaUg0VUlPUlBRaVhxZTRrUDRFR0tOR29TK2VuN1htZzNtYUNFSDIwek1iOXdkaitSV3ZPTTAraDZ4TTVRQjRLN1EveTYyb3NIdGZ3MUZUWVNsZ3BKZklpUm5WMGcxZEtpRnB2MElNZ0R2bHhXYWJNRmVaQVRjcVgzSHNLd0tSaWtjRnVYUXphSGNzZXI2QWZqOTA1TmQ5MmEwUnpnMHBXNzNvSlc3dzJ4TU5Xa1owWmtiVEJwUFdlL2htS2hoZGVybmtnTmdUOWZramVhcUdxelNaeGhKQTRoQitZSlJNMGdRcnYrUlBWN0tpR2ZoTW9ZKzdVcVpaQS9lMVVRQ2FnMmx4U2w4cm5QZitzdkRzdk5pN3VkSS92K3A1NmhMdHJYK2lTdTY5SVBJSmFJcG1lMUlhbFB1SER5UkwxN1VnS3RkM0k4TFpvWFJiQnNHMjN4QmppcE5QVGs5bnE2Nzl0OTR1Z3lMeklzNzJXSFBLTW5wdUxQNFQweGpuN1NVVlIiLCJtYWMiOiI1YjVmNGMzZTAzZGRlYWFkYjgzYTE0OTg1MjRmZGQyOWUxNDYzMWIxYmJlMWExZGM5NDY4MjhmOWRkMDBlYTY5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:42:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBoZzc5dUFyWFlFczM5ZTJib0NvUVE9PSIsInZhbHVlIjoiTVo1azM2SUxHdzFNNVRDL3N4NkxKc1FIZlRIY2lxaVg4aDFzR00yTXpsM2VQMGdseUIwdnRyb0Z2VktodnJWaC9SUktNeGtDWm5xbldSVkRubUZtRWNLMVg1dXRRTE94ZkpXNkRIMEo2ODRRMndRckN6dm9OMk5oblhhcTl6WHNPcE9jaUZmZEREMzQ5aTdMV3MvemNJeUZiRWp2T2RUeStwaVBnYVIyMjd0MGozd2VlZ2pKdHZ1K3puS284ellSNERJd1pYY3A4eHdpL1FPMWhmM0tYYlFGSEQ0dDFmZjB2dC9zY0M0cHFjcEpKUGxjSU5SWVdqSXdNQnBvdGd2OWdpcTFDUkxSM2RFekd6UFBZY0NnYy9ZaEtVTEcrQjJldTRidjNYSFdXZTN1RDJERnNqaVVJOE0yNnhqaW52SVZRd1FEcFFOY3Z4QXhxeWJFNnl5eWJiN3ZqUnpVTkVMZHFiMmNocEw3azRGaG8vRi9Wa2dXQUdsTG9DZlYyT0NCT0xvcXFMSk0zb2s1Wkk4RGlNVUppRWVqUnlIdVdubjFSV0I1bUtPZDhkdWhoZXhldHI4UkJST0NwaW1kWVRaNXpsZTRLYUUxUW9PUUkxSFFGU21VK0pyVER1aUtiTTlyeisrM2tyTndUcWRLejRDMVZoWkt2OGhDQkVLOEQrM1kiLCJtYWMiOiI0NTUyOTM5YjQ4NjJlYzA2OWQ4N2E0YzFmZjYzY2EyZTk4YjE0ODAzZjNhMDc5YTAyNzU4YzAzZGY4OGE1ZTNiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:42:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVrMzRMcUhsRkFXZ1g0cXJnN0RUOUE9PSIsInZhbHVlIjoiQko0RHNZelhQS0ZmQzlIMG94MllpbUtoNnBWUFFkY3o1V09GbkNEdHBJSGwvNXlObWdzQzNMdHVQVEZwVVF0REY1RDlqMDRWSUFSdGhIY0xsdWZhNnVMcEpyaDhTclY3TkEvM1U5dW5nSzR1eWxFRnFHZGd4K2FOTGYwQXFWWEtJaUg0VUlPUlBRaVhxZTRrUDRFR0tOR29TK2VuN1htZzNtYUNFSDIwek1iOXdkaitSV3ZPTTAraDZ4TTVRQjRLN1EveTYyb3NIdGZ3MUZUWVNsZ3BKZklpUm5WMGcxZEtpRnB2MElNZ0R2bHhXYWJNRmVaQVRjcVgzSHNLd0tSaWtjRnVYUXphSGNzZXI2QWZqOTA1TmQ5MmEwUnpnMHBXNzNvSlc3dzJ4TU5Xa1owWmtiVEJwUFdlL2htS2hoZGVybmtnTmdUOWZramVhcUdxelNaeGhKQTRoQitZSlJNMGdRcnYrUlBWN0tpR2ZoTW9ZKzdVcVpaQS9lMVVRQ2FnMmx4U2w4cm5QZitzdkRzdk5pN3VkSS92K3A1NmhMdHJYK2lTdTY5SVBJSmFJcG1lMUlhbFB1SER5UkwxN1VnS3RkM0k4TFpvWFJiQnNHMjN4QmppcE5QVGs5bnE2Nzl0OTR1Z3lMeklzNzJXSFBLTW5wdUxQNFQweGpuN1NVVlIiLCJtYWMiOiI1YjVmNGMzZTAzZGRlYWFkYjgzYTE0OTg1MjRmZGQyOWUxNDYzMWIxYmJlMWExZGM5NDY4MjhmOWRkMDBlYTY5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:42:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703132685\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1396742497 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396742497\", {\"maxDepth\":0})</script>\n"}}