{"__meta": {"id": "X78dfc8ff2b1eb9240432b5d333045e83", "datetime": "2025-08-02 16:37:42", "utime": **********.497064, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152659.564572, "end": **********.497136, "duration": 2.9325640201568604, "duration_str": "2.93s", "measures": [{"label": "Booting", "start": 1754152659.564572, "relative_start": 0, "end": **********.329991, "relative_end": **********.329991, "duration": 2.7654190063476562, "duration_str": "2.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.330023, "relative_start": 2.***************, "end": **********.497143, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "agd3tDEDk2VXewJzeDpM9OynoLwCzCFxXzQf1Sw0", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-1399201588 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1399201588\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1420097056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1420097056\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-14762947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-14762947\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1499485983 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499485983\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-789664139 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-789664139\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2020083125 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:37:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing3dmt0OUNuRHNXT0M1R0M0T0xleGc9PSIsInZhbHVlIjoia09CdWZ2WDQwMHNrTnY2ek4yYUJlK2ZFRlJNTWdmNkdQV1BPMnlGb20zQzRQZ3Z3eENDTXZGdGozMUZGWVVkVVlROWVBU2Vaa0Q5b0dWRzJRTm43RWFxdkZVSnJ2T2t1dGhEVmNrYnd6N0dzNlR0THd0U242T041ZE9GeW9zcE9tbUpza216ZURwdWYwcE02eDFjUnJPYTFaVCtKTENZZHJEN1Bvb2p0UU9BZEI0VG9nV3BYZVpXVk53alVpZTM5NEpYYlpFTEZvZFFteFBKTTFQYmpuVG9FWVhXVGpZMy9YZENEUk5lNndzY3d0YkNiQVpWWS9zSkU3S0pVTzllR2dRcDl0R1NCMVZLT25UNE1xbi9zYkluU1FON1dnVWp3QWRpRUc2Wm1iRzlXTG03Q0R2YVErSkxxaUs5T0VkOUVQekxQOHJrdXl5RVVnTlpXV3ZMZ212ZnM2ZGJDMWlGcEMva2wrbzNQYnFlVEFzZUZMakRnUTdqRXBZMmxWYXVHak5BN2t6ZkVnOUlBU2RxZ1pVSm1Bc0M3eHpjdXFvNXNpNy8wZHRxUWEzTkZWdmFqYnZ0WGFvN0h3UTFRTlliQjE0QStwN25Td2I0cGFjRXVyUGU0RU5mVGZOVEhHcTlPaXFwTUc5SjhhdzJRN0gxNDhtZW9lUXU3OUFrZWlzaHQiLCJtYWMiOiJjOWZmZjBkMGM4OWVmZWQ3NWUwNGZmYjkyMjE1ZDI0NTkxZTNhMmNlMDNlNjMzODk2Mzc2YzhlYTFhZDZkZWZlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Iituemd2V0tIN2tzQ0p2VU9wVmpQaGc9PSIsInZhbHVlIjoidDI2ZkNIVjhYb3JuNmZFTi95ZVoyRkNVeUNCcmdBLzFlTEhOVFEvYkN3RFRwOUhGc3JyeVdXVm9LZnhCMk95bk1Kalk3eWpCZTlrYzRJWGcwdlh1M3pMaEZISmorVXJXbG5xUXdTMDU1S3A2K1RpUFBQRHV6M2xzL2wweitWSmh1NUV6N1lnZ3JWckR2U1FrZklGeUp6YmR5dC93a1hGdmd0Y0dWTFl4YUp2bmpXNVRDVTZVdVB3UWNXUVlqOTdsaDE4YkdJTjZuSEVIUEFiMkRxbmdOMnJqQ0dpSnE0ZStxMkN2VStUTkVGNGx0SXNrNWtGSEFhaFh5NnRWbEp1NTl1RDZEd25NaVU4emxZSmZieitEU2pOSmlMQzcrZFNpc1ZMMitZMHByVDNrb0ltcmdrV2szSDZ1bndaeGlSVXRQNHlnV0tQMS83ZWNlcWd3U2srbkoxcGtNdzVocTN6bXdvcEMvc0tQUW5sOE51OG5xaUNhdHdlejRjYWdsZ3ZYQ2ZDUTNNUVN0bjhQYWJoenZSNWtGZlhOd0dmTWFNcnkrREtSYUdycVREdjdSL0d5LytqQXJOYzFOM1BYUE1yaTh6SHFQY0dhcjhRTlVtU3R0YmVoTjJLQ0llbmpEVDMza0RGZ0ZXbkl4bEhMNVloaHpmc2JEWjdZTkw1SFFZZmQiLCJtYWMiOiJhZjc5OGQwYjc5MDBmYWU1MTIxMTUxNjg4ZTQ3NWQ0OTYxNDc2NTJmNjY0MmEzZjc4OTAwZDRmZGIxNmU2ODFlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing3dmt0OUNuRHNXT0M1R0M0T0xleGc9PSIsInZhbHVlIjoia09CdWZ2WDQwMHNrTnY2ek4yYUJlK2ZFRlJNTWdmNkdQV1BPMnlGb20zQzRQZ3Z3eENDTXZGdGozMUZGWVVkVVlROWVBU2Vaa0Q5b0dWRzJRTm43RWFxdkZVSnJ2T2t1dGhEVmNrYnd6N0dzNlR0THd0U242T041ZE9GeW9zcE9tbUpza216ZURwdWYwcE02eDFjUnJPYTFaVCtKTENZZHJEN1Bvb2p0UU9BZEI0VG9nV3BYZVpXVk53alVpZTM5NEpYYlpFTEZvZFFteFBKTTFQYmpuVG9FWVhXVGpZMy9YZENEUk5lNndzY3d0YkNiQVpWWS9zSkU3S0pVTzllR2dRcDl0R1NCMVZLT25UNE1xbi9zYkluU1FON1dnVWp3QWRpRUc2Wm1iRzlXTG03Q0R2YVErSkxxaUs5T0VkOUVQekxQOHJrdXl5RVVnTlpXV3ZMZ212ZnM2ZGJDMWlGcEMva2wrbzNQYnFlVEFzZUZMakRnUTdqRXBZMmxWYXVHak5BN2t6ZkVnOUlBU2RxZ1pVSm1Bc0M3eHpjdXFvNXNpNy8wZHRxUWEzTkZWdmFqYnZ0WGFvN0h3UTFRTlliQjE0QStwN25Td2I0cGFjRXVyUGU0RU5mVGZOVEhHcTlPaXFwTUc5SjhhdzJRN0gxNDhtZW9lUXU3OUFrZWlzaHQiLCJtYWMiOiJjOWZmZjBkMGM4OWVmZWQ3NWUwNGZmYjkyMjE1ZDI0NTkxZTNhMmNlMDNlNjMzODk2Mzc2YzhlYTFhZDZkZWZlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Iituemd2V0tIN2tzQ0p2VU9wVmpQaGc9PSIsInZhbHVlIjoidDI2ZkNIVjhYb3JuNmZFTi95ZVoyRkNVeUNCcmdBLzFlTEhOVFEvYkN3RFRwOUhGc3JyeVdXVm9LZnhCMk95bk1Kalk3eWpCZTlrYzRJWGcwdlh1M3pMaEZISmorVXJXbG5xUXdTMDU1S3A2K1RpUFBQRHV6M2xzL2wweitWSmh1NUV6N1lnZ3JWckR2U1FrZklGeUp6YmR5dC93a1hGdmd0Y0dWTFl4YUp2bmpXNVRDVTZVdVB3UWNXUVlqOTdsaDE4YkdJTjZuSEVIUEFiMkRxbmdOMnJqQ0dpSnE0ZStxMkN2VStUTkVGNGx0SXNrNWtGSEFhaFh5NnRWbEp1NTl1RDZEd25NaVU4emxZSmZieitEU2pOSmlMQzcrZFNpc1ZMMitZMHByVDNrb0ltcmdrV2szSDZ1bndaeGlSVXRQNHlnV0tQMS83ZWNlcWd3U2srbkoxcGtNdzVocTN6bXdvcEMvc0tQUW5sOE51OG5xaUNhdHdlejRjYWdsZ3ZYQ2ZDUTNNUVN0bjhQYWJoenZSNWtGZlhOd0dmTWFNcnkrREtSYUdycVREdjdSL0d5LytqQXJOYzFOM1BYUE1yaTh6SHFQY0dhcjhRTlVtU3R0YmVoTjJLQ0llbmpEVDMza0RGZ0ZXbkl4bEhMNVloaHpmc2JEWjdZTkw1SFFZZmQiLCJtYWMiOiJhZjc5OGQwYjc5MDBmYWU1MTIxMTUxNjg4ZTQ3NWQ0OTYxNDc2NTJmNjY0MmEzZjc4OTAwZDRmZGIxNmU2ODFlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020083125\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-509462180 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">agd3tDEDk2VXewJzeDpM9OynoLwCzCFxXzQf1Sw0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509462180\", {\"maxDepth\":0})</script>\n"}}