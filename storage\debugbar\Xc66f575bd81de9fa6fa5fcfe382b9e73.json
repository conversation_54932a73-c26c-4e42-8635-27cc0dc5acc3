{"__meta": {"id": "Xc66f575bd81de9fa6fa5fcfe382b9e73", "datetime": "2025-08-02 16:36:33", "utime": 1754152593.632833, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:36:33] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": 1754152593.619061, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754152589.981314, "end": 1754152593.632888, "duration": 3.65157413482666, "duration_str": "3.65s", "measures": [{"label": "Booting", "start": 1754152589.981314, "relative_start": 0, "end": **********.094981, "relative_end": **********.094981, "duration": 2.1136670112609863, "duration_str": "2.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.095009, "relative_start": 2.1136951446533203, "end": 1754152593.632893, "relative_end": 5.0067901611328125e-06, "duration": 1.537883996963501, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50731024, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.736288, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.062020000000000006, "accumulated_duration_str": "62.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.585016, "duration": 0.022850000000000002, "duration_str": "22.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 36.843}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.641715, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 36.843, "width_percent": 2.064}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6574411, "duration": 0.03595, "duration_str": "35.95ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 38.907, "width_percent": 57.965}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.705108, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.872, "width_percent": 3.128}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-963497224 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-963497224\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1790864528 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1790864528\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-448955381 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448955381\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-910502684 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZOanVKWDFjOWRqaDZBTkplMGN2UGc9PSIsInZhbHVlIjoiY1h1aXdhOXJaQkdncDZHQXJ0SS9HTC85enNLalB4Vm9KUC8yTXlCUjlNaHM5RUhQVUxTRTZoYWl0TDNBNjB2YXo0cW1EbXdWWmYyZXZVRm85UEhLL0NqalhEMlhtd3JtUkRhRm1TRHY5U25JMS9ZK2dLU25sWDhVUUthS1kySmtqdFVCZndPVUcrV3pmaDZ6OE9McnpRSWlmK1VKM01UM0hYWjViSld1NE9XMHJxajBuYTY2UnFlT2RKZklYRE1hbmRiZHhBWkdyTW9vOTVaYUk1VFdwTnNSeFRtc3RreVV3QkRNQ280Z2ZmODZiRW90VjByaGhGVlVVS040T24xWjh3Ui93T21uYnR5bGNiejFqallhOE9FZjVRb05YYTZCbjM3WUZOS0JLVzRTZWZzR1hmdXc4azJqTXU1dVNMK3dRbWx4NFBYeUxpT1JmNm1tcVVHU3VoVnlWVzZQLzBsZ0tyWWdRM2h1aGdlVVgrMlBQTkxsTVIvMWlLRm14aXg4Zzk3ZmxnMlR5SkEweHVJa1dMQ2RyaThQTGhOTXNYWEg1dGxSQ2ZzMUNOaHkxa1pFNEpzYnl1U3Q5OU9mSEd4WkEwRGdzSTB5bjNmcmtXSGQwR3VKK3oyTnpqN2RVbCtiNzZRbkhjU0tUNUFqWWlRTXhDWkljWXUvUlo2M2lXekciLCJtYWMiOiJlZmI1MzFkNzg2ODk4YmZhYjY2ZjY4OTUzNWIwNDZiYWI4ODgwOTY2MWM3YzY4ZGZkNWY0YWVkZjkwMTIyZjQ5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InYwQmdtaWN5cXlsQnFnbi9mK1pLSHc9PSIsInZhbHVlIjoiVTR1M3VWRi82am9HdXIzUGtXU1dSblVva3U2ZkxHTGVSdHZGN0lOeVYvUFNHc2laZk1jYXQ5UDl5SSsxVXpFbThONGRaakhwdngrZThIL0FLblg4QUJUUllpZGtGRE8wR2ZZUmJHMVloRjBCVVplaS9LaVg5dzkxUGF3aFhpcmhGME84NHljVmxCUHN6YTNCMHlWSnBFNExpd2FRUzlwRVVGQjFHZEZvSHI5VmlRVStXazQxMHgwMFNIbE92UDlGTFZNQTFsaVFqWEZ4d2xPTERROWpVT0dUcjNjcENVZHlTU0tWTWhYb1VRY2dTRDJ2b3Fsc1BEcmRIWEh6MmlPMzBXSGQybkhXblBrVzQ4K2QxZlNuWW8zQ2JwRGQyMkhoM0RQNDNBcGFTby9yQ3N3Umh5bHRVaW9GUi9lT2ZUOVNKb1JOQXRDNDZpZWFyOWxtY0creUxJV09tR1lXeUJTSERRNmpCeGNveFhGVFJkVTMxWHR0a0RQL2cva3VtQ2RxOHA1aXVHazVvZThsVzB5aGwvcS9lVjkzbHR5WXhkMEk0aUhxYlhsU2xrRmlJQk11Y04rZDdlNmJSNHplMzNtNGM3Q3RWTksyVTNwYU1VTjFvRzZwNVlIQW1BdGpUQ25FSFJEdDBzY0NkMGdGeGpSOHI0Szc3amlWYzhSMmc3UGkiLCJtYWMiOiI5MjJmN2QxODYyOWZmNDEyMGQyYWE4Mjg4YzdkMDdkZDViZmFjYWYxNjU5MDM3YzM1ZmVjYzViNzEzZjE0NzRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910502684\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1794344940 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qX0CnOObck67LLGXwnxE1rbA703TvCmAFmaCZvwn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794344940\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1346096780 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:36:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNLVU1TeXRxTVR6dC9xYVFjY3JBK1E9PSIsInZhbHVlIjoiTk9iN3RHMFdkM1creHdkUDNsSjM5ZkVMUlVMRXFmOUhWeWV3aXN0WE5hZjIzT21pWjc0aTdnVWxWUU1NT1VFS2phTEJtdkVzcHJEL3N1VHVXS0JjR3JBZVJybWtDUVUrOXFTcnB2ZjJkVTc1UHJMeCthcDNNdFJKZmxWWnRzTXNaMjdtWnVaZVJhK3NUUzh6WXFFS091QjVzTkVEdTZMSnowNVhjWG8wVCt3c1ZLZVdLeWpBTzZWMTFXamdsSEhtNVg2bXRkOGxzQjE5ckZjR0RZdWpSWU9VaWdEaVhZSVJCZzk4Z0ZqQWNPR2ZrYWJodittWnE4L2tDZDhlRVhGdXhQVVYrSnRzUitkbkRQT053aE84emx5TlpLMVk0VmtoVUh2UXpUV2ZEeFRGQWp1MzJYeDE1Wmw4dTE5amowOTRVNFFIblJGaTlnZHBEdEJzb1pzcldTUUkrQitrdjFieTZCV0c5dVJSd3dvcHNsaTFJck13VEZ5d3E0RlhYM2M5M0NGdkFveWozWDZjWDlueWJmdGRPdmFNblJwN1NnL0xJaW1Ia0RTUmIyVzFyaE9pME5LdVozc3JkVkszZ0JlZHRFQWtrUU5SSDF1QlhJVWszVHo4NHE1azJ6TGZtVHFZbDJxSlZ3M3dEZzdtVHl1MTFNYW5xMm5YVit3NmlBS1kiLCJtYWMiOiIzMGE0Yzc0ZGMwYjUxODUyNzY5MzVmNDY2NjgzNTgwOWFhN2U0MWFjNDQ5ZTNhYTg2Y2M0YzliYzE4OTZlZTUzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:36:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkdLaXA1OGdtMG85RHZUS0cxMmRDclE9PSIsInZhbHVlIjoiem5OVnVWbFVnVWl4cC9taVdLNDhudmZSMmtZclFacDJ0K2ovT2h0UGI0VkdXYzBJRngvL05PZmtEbnUrbmdPWGhvOFZHYkFWenFNLzFxVU9vTTl2eWRRYU15Mzh3Nmxqdk8rNTh0ZUg4NUYxbVVUOUhKVWRIMDFvQWE2ZEw5RDVzWkpzVWczblhJT0tncWdMcllDNGx1dDFDMXp6ZVpES3lQeFdiZHZoNm5oS3A4eEI4ak9TVVBmS1BBZkhyZFRUbkhsdGxpN2gxbUZGRWlXSHF1QzhWSExtQlBhc1pzUGRmUHpuVjF1dFp6NE0wN1huTDZQRnk4bFVCWHU0Z3kwbTR1eWtIUzhxOUVneWNYTk1WbnBOS054bDBtWEpMN1lwMnNSckEyaEtsYmpvelVtQmxKbWNYUTVyMFlzSDJCZkZMOTN5bHRNZ2hGcHgzRzg5RCt6RThLbS8zc2hTTFIwRGFML3c1ckd1MFNrZlR2L3AyaGVYUTV4UkxSVFNMVFVMd2oyazM3dEhqYkkvVmlJSTRLcTNQK2trZzlyRHpJdnkzaHpjU3dZZXBBdTk0b3MvRmZWNExpdTJTNDU3cHhDZUNwRWFIRUNqK08wR3BPRCtjMGpZbkZRVzNqckVGN1RUTkJkMitPUkhnVENhOHlzL1VXbjdMVHlYNU5UVjFvczYiLCJtYWMiOiIxOTEzNjM1MWFmMWFhYTVmNDU1NGQ2ZmUwMDY5M2M1ZDM1M2VhMDE3M2NmYzVmNDYxMjc1YjIyMjBiMjQxNTBkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:36:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNLVU1TeXRxTVR6dC9xYVFjY3JBK1E9PSIsInZhbHVlIjoiTk9iN3RHMFdkM1creHdkUDNsSjM5ZkVMUlVMRXFmOUhWeWV3aXN0WE5hZjIzT21pWjc0aTdnVWxWUU1NT1VFS2phTEJtdkVzcHJEL3N1VHVXS0JjR3JBZVJybWtDUVUrOXFTcnB2ZjJkVTc1UHJMeCthcDNNdFJKZmxWWnRzTXNaMjdtWnVaZVJhK3NUUzh6WXFFS091QjVzTkVEdTZMSnowNVhjWG8wVCt3c1ZLZVdLeWpBTzZWMTFXamdsSEhtNVg2bXRkOGxzQjE5ckZjR0RZdWpSWU9VaWdEaVhZSVJCZzk4Z0ZqQWNPR2ZrYWJodittWnE4L2tDZDhlRVhGdXhQVVYrSnRzUitkbkRQT053aE84emx5TlpLMVk0VmtoVUh2UXpUV2ZEeFRGQWp1MzJYeDE1Wmw4dTE5amowOTRVNFFIblJGaTlnZHBEdEJzb1pzcldTUUkrQitrdjFieTZCV0c5dVJSd3dvcHNsaTFJck13VEZ5d3E0RlhYM2M5M0NGdkFveWozWDZjWDlueWJmdGRPdmFNblJwN1NnL0xJaW1Ia0RTUmIyVzFyaE9pME5LdVozc3JkVkszZ0JlZHRFQWtrUU5SSDF1QlhJVWszVHo4NHE1azJ6TGZtVHFZbDJxSlZ3M3dEZzdtVHl1MTFNYW5xMm5YVit3NmlBS1kiLCJtYWMiOiIzMGE0Yzc0ZGMwYjUxODUyNzY5MzVmNDY2NjgzNTgwOWFhN2U0MWFjNDQ5ZTNhYTg2Y2M0YzliYzE4OTZlZTUzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:36:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkdLaXA1OGdtMG85RHZUS0cxMmRDclE9PSIsInZhbHVlIjoiem5OVnVWbFVnVWl4cC9taVdLNDhudmZSMmtZclFacDJ0K2ovT2h0UGI0VkdXYzBJRngvL05PZmtEbnUrbmdPWGhvOFZHYkFWenFNLzFxVU9vTTl2eWRRYU15Mzh3Nmxqdk8rNTh0ZUg4NUYxbVVUOUhKVWRIMDFvQWE2ZEw5RDVzWkpzVWczblhJT0tncWdMcllDNGx1dDFDMXp6ZVpES3lQeFdiZHZoNm5oS3A4eEI4ak9TVVBmS1BBZkhyZFRUbkhsdGxpN2gxbUZGRWlXSHF1QzhWSExtQlBhc1pzUGRmUHpuVjF1dFp6NE0wN1huTDZQRnk4bFVCWHU0Z3kwbTR1eWtIUzhxOUVneWNYTk1WbnBOS054bDBtWEpMN1lwMnNSckEyaEtsYmpvelVtQmxKbWNYUTVyMFlzSDJCZkZMOTN5bHRNZ2hGcHgzRzg5RCt6RThLbS8zc2hTTFIwRGFML3c1ckd1MFNrZlR2L3AyaGVYUTV4UkxSVFNMVFVMd2oyazM3dEhqYkkvVmlJSTRLcTNQK2trZzlyRHpJdnkzaHpjU3dZZXBBdTk0b3MvRmZWNExpdTJTNDU3cHhDZUNwRWFIRUNqK08wR3BPRCtjMGpZbkZRVzNqckVGN1RUTkJkMitPUkhnVENhOHlzL1VXbjdMVHlYNU5UVjFvczYiLCJtYWMiOiIxOTEzNjM1MWFmMWFhYTVmNDU1NGQ2ZmUwMDY5M2M1ZDM1M2VhMDE3M2NmYzVmNDYxMjc1YjIyMjBiMjQxNTBkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:36:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346096780\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2089620698 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089620698\", {\"maxDepth\":0})</script>\n"}}