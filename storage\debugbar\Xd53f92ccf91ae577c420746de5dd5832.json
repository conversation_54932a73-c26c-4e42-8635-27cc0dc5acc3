{"__meta": {"id": "Xd53f92ccf91ae577c420746de5dd5832", "datetime": "2025-08-02 16:39:02", "utime": **********.218576, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:39:02] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.205506, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754152739.204882, "end": **********.218642, "duration": 3.0137600898742676, "duration_str": "3.01s", "measures": [{"label": "Booting", "start": 1754152739.204882, "relative_start": 0, "end": **********.854363, "relative_end": **********.854363, "duration": 2.6494810581207275, "duration_str": "2.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.854429, "relative_start": 2.6495471000671387, "end": **********.218647, "relative_end": 5.0067901611328125e-06, "duration": 0.36421799659729004, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50731456, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.161948, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.06488000000000001, "accumulated_duration_str": "64.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.983936, "duration": 0.023030000000000002, "duration_str": "23.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 35.496}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.054586, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 35.496, "width_percent": 3.021}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0741901, "duration": 0.03717, "duration_str": "37.17ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 38.517, "width_percent": 57.29}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1247578, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.808, "width_percent": 4.192}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies?per_page=25\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-422593103 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-422593103\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-278354399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-278354399\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2130091693 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130091693\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2033347303 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/system-admin/companies?per_page=25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhxdFJlYmF2MmtCQndVSkVSTW9kanc9PSIsInZhbHVlIjoidlVnem1YOVNjTFA2djBXZVJubktkdklqLzdOam56eVQvWFdNSFFiU2g5d0lDRTJabGFieVB3QnVBM0k3R2lpZmJLNGNhbjY2OHpab3ZCNk5xalMwVUg4WE1QcVJkYWltZ3BZTWFuaUdVVWcyNEtJYWk2V3dJcnRRYllMWnNOc1BMUU9yNi9SWlNiYWpOY3Zzb2crS1VlMi9OMkZOSHZ4L1Q5VkdYcWdGekNNSHp3VjYybEgzWFF1UGhjQU02TTc3Z3ppV3BLcnRKTXJ4SXEzdCtLdVJrYmwyOGx0SEUyTjR5UFlxeXJhQzdHSytJQjM5VkJkR3lFdnU2TXlOaUVYMVlXa1R0OG5rckJ5WFRXdzFHTy83REhXRnRIZW12NThJN0FhSVJBOThXN0RkQlg1ZHR5MGpCNFV2S0pqZ0JZc0MrUnF5TkNIcTZIbTBKelZUT014OTg1ZForN2FGOVJzQTdqSlJqcGs4MkZmSFgwQ2xPTW15Q2x1My9DcmNQTnY5WE1sc3pKLzh3SzdLcEVFWjlKSElPVGRrc2dKRnM1OGxaNWxWWGdEUHVieEZTdkdRZm9tWDRISVMwdzJySHBHSitCckhRQ1VvUVp2ZDBybVFZSWx5cWFIcmowNUpIeW02Nk9LR1VaR0IzVGJYcllGbHlnU0FJMEIzNVRTYlUyRmUiLCJtYWMiOiJmZDJiZDliNGFlYWIxMzUzOGNlNmM4YzY4ZTljMTcwNjkwY2FmNjQ1NTYyZWYyYmY2MWZhOGI1YWQ5ZDRkMWFlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjhhTzgrM2swVURWNUM5aVpCdUFSenc9PSIsInZhbHVlIjoiKzZNazMwUjBYYlpsbURrV2VleVJrdVBqMlZHL04xTDh2Ulhqd3piakVCbnNEM1h6aGMzZXVicmhpNG9QTFJrMXYxYVBxSC81ZGg5QkJJb2dYTjU2UmZHRTJMM0pMR3pFQjdHZ1V3Yjlvb2YzZnZTSGNvbjBYem5DSG95WFdhaW4vK1dpK1pJcHNZd3dhOUVMNUZWdXY5Y2NhOTlmeHhEeVFaVkRIZXZFakFXRUoxd1I1TkwvV1NPQUdjUGdsNDcyRjJIWEVFQVp6bHVXMUs2REplMGlUY0lJeHRSYnhaSmkrZjloUDZPVThrWVR5QlkrREZqRDkvTVhYK1NFNTVGODVwdVpvQ05ZZnhyNmprR0k5Qm5sdVV1ZndGelh0eTQyYm9RRVd3RVpaYnA3MElvTzFraG9jT00yK1NCQUpjUkw3VGluVFB1RnhiR1ZqOHBJS3grb21hRnRWakM5ZUJReVhkakNMaDE2UjdrUk1jZExldjZOTzFQMlYvZmdjU3ZxL3BBbkJUMWpkL1d6YUJsem92ZnZDMEJZUHZMMUxxZ1llOFo0cjk3U0dTV245VEpJTUN5RzJXckI2MHNmcEFCdVB0b2VJT2VEeTNPbXhmTXJRU0l2bHpmMXZYeXFjT2NoZDlWUXZvTlZYd0ZVellidzRxVUd3elNrTW5oUDlHSHgiLCJtYWMiOiI4NjExZTE0NTIwZTI5MWJlN2RjYzgzZGM2NDFmNGI3Mzc0OTAwMGY2NTI4MWNhZjI0NWU1NTE5NTZkNDlmNTYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033347303\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2056845412 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qX0CnOObck67LLGXwnxE1rbA703TvCmAFmaCZvwn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056845412\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-448101963 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:39:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJsczMzSHgrUU54c2JqZzZNQzM4cWc9PSIsInZhbHVlIjoiU0p3K1hjV21IZExFdDBSZHl5NlVTalQrMFZza1hiRnVzS3FpSUQ0YlMySlhKRXZKcVVwK04ycW9ZRHZvZ2cyMllpa09IUkpqTmFCRGpkcXlla3ZTMHNTc0tSMENGVXZDTTM1Vmh4c2k4czlOQ0E1TlRnM0N6STI4ZSs2emdwZWdRODB3ajFsVE4rbTl2S3Z4NmluY0Jvb1B0eEpNdmFQTE9BQnA4b21tSHE3RkpKL1piU25UbWpoSUZqKzB3ZnZsM2dtT1FGb3piNEN5cW00TFVJVDhCVjNyUTdjUmtSV2ZIT1QvaGRXbDd4d1lCb2hPaklSR1JsU0VneW8zUi9WMHVuMFRMaVQ0MDBGUmEyd3U0UVFPRUNPOGZ1RnVHVVdEbWJmYThnZlJEL3E1VXN2Ry9Rc1h4OW5Sa3czT2FvWDZOUmN6QjFnVHRDQ3VkSTdnaWtpYzc4UjI0M0NqVHpVc2ZjMXlXMEV1UDRwUUdwS2FpTndlTytzTExvQ3Fjb0xyVUlkc0tqNStDR0x1cG5jWG5FdkxHSXFPbUxnVG1YSWNndVBLU1lMamt4dGdndlBDUHJxaXZHUDUwaWdUSHVaWjFLdm5ZV0k5MW5CWU54d0xsNC9JQ0dIWGFXaWVPam0wblN4cEdpM1ZidjZ2NTdOUmZnUGJsbTBQVklJT2E0MzMiLCJtYWMiOiI5MGRiNjFkZWY3OWYzOGY3NzQ5NTI1ZDg0MzM1ZDEyMTY2YTNkZDM1MTcyMWNlMjA0ZmJmNzk5YmJmNWRmNGI0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:39:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlM3b0Q5STFDN1Y4ZllnY09qZ0VKYUE9PSIsInZhbHVlIjoiTzVoK1VTVWFPKy9CYnBTbzIxeTBWZnNZS1VjZ282aGdkbk5rUFVXU0VLczBZcTZoK3J3WWUzZFAvekYwSm56eU9iaG1GK2dwY29YUlV3dVIrRmNzcFBONXNvTmtzU3pvN1JUWUxaalphWlBmRWdqb3pLU1JTamJTTWdwSlRSVFpHRENiNTZmVDhjN0NuREgwZFpEdXhmdTdhRUhXSXhNY1BTUUo5OEJHSFJHTFJmVk1sSWNIWWtuMmxqZGFDaDBCQ0lzWFVPd0d1LzM3NktybU9NK3JRQjlvWWluNzVjVGVXUlFCRWQvcElsK04xNW9DQXhlMW9RRHRMb216bDYzNU9menFCK1dUSTgxOHJueHYxcG8vZWUvVW1aK0Q1Qm0yUllDYUM4dXdMU3grVEd4WGI0TWhKR3oyNWtsV0lQY0lwMDU2Nkh0RUNRSlA1c3k4WTFleW9VeFA1MTZHbXEwaktVc1VIYUNYUTRHQVJramMrQjlzTTA2VDFHMlZjVm9TVE5uQVJ4SFZPZVVDZjdHUTg4bVA3ekdrVGx5clpScm9vdTBZTUcxcG1taG84bEh4a0FTb0VmNjhLQTl2ZC9SaEQ3MzRQZU5PZ1BlaGRTamovRm1FSTBUWUhwVUFOS0dkKzZNQXExV1Yvd2lpYzFJQnVFTHlxVFVsdnRuRW9ldkoiLCJtYWMiOiIwZWYwNWJiYWZkZGU4OGNkYjY1ZDc2NjI5ZDU3YWVmNTUyNzAzMGFiMjk2YzhmOTM2ZGQ2MTZlNDc0MTY1OGE2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:39:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJsczMzSHgrUU54c2JqZzZNQzM4cWc9PSIsInZhbHVlIjoiU0p3K1hjV21IZExFdDBSZHl5NlVTalQrMFZza1hiRnVzS3FpSUQ0YlMySlhKRXZKcVVwK04ycW9ZRHZvZ2cyMllpa09IUkpqTmFCRGpkcXlla3ZTMHNTc0tSMENGVXZDTTM1Vmh4c2k4czlOQ0E1TlRnM0N6STI4ZSs2emdwZWdRODB3ajFsVE4rbTl2S3Z4NmluY0Jvb1B0eEpNdmFQTE9BQnA4b21tSHE3RkpKL1piU25UbWpoSUZqKzB3ZnZsM2dtT1FGb3piNEN5cW00TFVJVDhCVjNyUTdjUmtSV2ZIT1QvaGRXbDd4d1lCb2hPaklSR1JsU0VneW8zUi9WMHVuMFRMaVQ0MDBGUmEyd3U0UVFPRUNPOGZ1RnVHVVdEbWJmYThnZlJEL3E1VXN2Ry9Rc1h4OW5Sa3czT2FvWDZOUmN6QjFnVHRDQ3VkSTdnaWtpYzc4UjI0M0NqVHpVc2ZjMXlXMEV1UDRwUUdwS2FpTndlTytzTExvQ3Fjb0xyVUlkc0tqNStDR0x1cG5jWG5FdkxHSXFPbUxnVG1YSWNndVBLU1lMamt4dGdndlBDUHJxaXZHUDUwaWdUSHVaWjFLdm5ZV0k5MW5CWU54d0xsNC9JQ0dIWGFXaWVPam0wblN4cEdpM1ZidjZ2NTdOUmZnUGJsbTBQVklJT2E0MzMiLCJtYWMiOiI5MGRiNjFkZWY3OWYzOGY3NzQ5NTI1ZDg0MzM1ZDEyMTY2YTNkZDM1MTcyMWNlMjA0ZmJmNzk5YmJmNWRmNGI0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:39:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlM3b0Q5STFDN1Y4ZllnY09qZ0VKYUE9PSIsInZhbHVlIjoiTzVoK1VTVWFPKy9CYnBTbzIxeTBWZnNZS1VjZ282aGdkbk5rUFVXU0VLczBZcTZoK3J3WWUzZFAvekYwSm56eU9iaG1GK2dwY29YUlV3dVIrRmNzcFBONXNvTmtzU3pvN1JUWUxaalphWlBmRWdqb3pLU1JTamJTTWdwSlRSVFpHRENiNTZmVDhjN0NuREgwZFpEdXhmdTdhRUhXSXhNY1BTUUo5OEJHSFJHTFJmVk1sSWNIWWtuMmxqZGFDaDBCQ0lzWFVPd0d1LzM3NktybU9NK3JRQjlvWWluNzVjVGVXUlFCRWQvcElsK04xNW9DQXhlMW9RRHRMb216bDYzNU9menFCK1dUSTgxOHJueHYxcG8vZWUvVW1aK0Q1Qm0yUllDYUM4dXdMU3grVEd4WGI0TWhKR3oyNWtsV0lQY0lwMDU2Nkh0RUNRSlA1c3k4WTFleW9VeFA1MTZHbXEwaktVc1VIYUNYUTRHQVJramMrQjlzTTA2VDFHMlZjVm9TVE5uQVJ4SFZPZVVDZjdHUTg4bVA3ekdrVGx5clpScm9vdTBZTUcxcG1taG84bEh4a0FTb0VmNjhLQTl2ZC9SaEQ3MzRQZU5PZ1BlaGRTamovRm1FSTBUWUhwVUFOS0dkKzZNQXExV1Yvd2lpYzFJQnVFTHlxVFVsdnRuRW9ldkoiLCJtYWMiOiIwZWYwNWJiYWZkZGU4OGNkYjY1ZDc2NjI5ZDU3YWVmNTUyNzAzMGFiMjk2YzhmOTM2ZGQ2MTZlNDc0MTY1OGE2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:39:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448101963\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-314373621 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/system-admin/companies?per_page=25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314373621\", {\"maxDepth\":0})</script>\n"}}