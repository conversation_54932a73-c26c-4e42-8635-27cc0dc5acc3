{"__meta": {"id": "Xfc3da9de1b47eed7cd13a41a0c389b1e", "datetime": "2025-08-02 16:37:36", "utime": **********.566104, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152653.641847, "end": **********.566191, "duration": 2.924344062805176, "duration_str": "2.92s", "measures": [{"label": "Booting", "start": 1754152653.641847, "relative_start": 0, "end": **********.299269, "relative_end": **********.299269, "duration": 2.6574220657348633, "duration_str": "2.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.299317, "relative_start": 2.****************, "end": **********.566199, "relative_end": 8.106231689453125e-06, "duration": 0.*****************, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qxuNZl2e72jhTUnEQfcLkaI4AHDrGJklMQkQ4RIr", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-978084079 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-978084079\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1322804001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1322804001\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2126765482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2126765482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-476245553 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476245553\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-316588049 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-316588049\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-784072412 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:37:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF2d1M1ZHdCdDBTeDE4eVhhRkZ5b2c9PSIsInZhbHVlIjoiTy9uU0RLbEtmQmJsdmtyT2ViY2d1M3JDU2tzWlR5YkFldGlPTnlIdjhUR2VZN0FkR2lWZnAvMmxlZGlWbVMxSEVqeFAwWHJESDZDU3NVZTBjQmx6U0lWdGZvdFhkTytiVDd4R2E4RVcxNEdjWEl1cXIybldEVVdXcWlldUdXamhxZk5KZnM1MDYwZW5EUHA3M1dZMkZSa1dmb0JIZ2Z5LzNxbWxyM09EU3o0c0g3OTRYVUNsRjFiRk1YcUM3NVo2NVhRdTBtWm5CanlERS9yNExVUjdlamh4N1A3Wk5iNk43a1pQNEVUaDh3emprdThaQ3VPSWNiSHpKLzMzYmpRaDgxVkdYZGRHTDdpTmtZaE1MUVZOczFqeVdDbFpNdW0wMjlDenFscmFaU013VDB5Mk5ZSzdxTFpFQlBQU05sQUNJZnJ4YVRlcUZ3cGR4LzQxbzVNb0lOMCtmMFhYdDM2WndEWUVNdlRIdis0QnpOWmJuQUVDbnh4M0VtUEY2dzNJNjNUT2lHYjJOUWNnT29TczM2bzhXRXdnQ3plRTRCUStuRWs5RFdBc0w5UlZyTXpKbnNPQWxYWnlqbVBFWHBHVkpQUStvUWprOWEvOVZTWXcxbU9oMHJ0c1RobWFhVSsxVDhhcUpNTTA1OVpoVG9meXBZRFU1KzV4SzRLNHBzcnoiLCJtYWMiOiIyMjcyYjZjZWY2MzA4NTk4NDFkOTBjNjExNzE0MmZkNzA1NzcyMjY4MGViOGZiNzhjZGZlZjVlOTRlZDI1MTI5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNJY0VkQ1BmOVhBUTh0ZFVyQkU0L3c9PSIsInZhbHVlIjoicmltTXhzV29ZMWd6R3lhZFQwa0FxUWZaZVZwKzJqYjVOTFUxaWJ0LzdMa1NZTnNtRlAxcVZBMjlKT095b0pXcVRiaDA5QnRxSExVTFB5d1ppVitPdmxWcGMrRGp5RHhhRVpqY0Z6LzRYRnRTNVJLeUd2WGtGWDVXaktrMFd2Y05EOVVMSFo3eHFOd2ljNTBwVFVFaS9CWk4razd0eXNkYUlkMjNCZHpLS3dNU1p0Y2RJRU9IelAwaTEvZERKL1NJZmpMNEhmekhWdFRpK2JGM3RDWElvQXVxaXlLZVg1Y0d4bTRnb2VsOHBvcm5wRkhsR2tURFd0Z0FSNkhERXBKK2JjaE9kMzh0a0NZVVBIOUFQeUhNdm5Qd3V1M2RUWDJKalZEMXRaMkplMnd3Yk1tZmhvT3d0OVhJZTIwVWtNajZZK01CMWU0RW51Tk9vc29ReDg4NVJ4RUdQYmZYZStNQ3BPdzdDT3pOZUZaR3cyQ1JNQnpnTytwUlZVWFRHOXRobXdCY1NETjkzSlNRK1didHcvM0IrNTFPcGszSS9iK1ZLeUMxY2h4VjJyeEw2TW44OEdPNFIwZnhYamhhcHhKUElSMGVOQ3crWE4xR0tVUWNubFRzRzRjSlRqL1ZTaWN1S2F6VER4WlFKelBVRWJOOStvcTNvNHJkcWNQZjBrMG0iLCJtYWMiOiJlMWVhNzQ5NzMwYTZkYjU3ZjUzNTFlNGRmYTIyNzkwOTdhMGE1NTE2YmUyN2I2YzFiNmU3Nzg5NzlkODExNGI1IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF2d1M1ZHdCdDBTeDE4eVhhRkZ5b2c9PSIsInZhbHVlIjoiTy9uU0RLbEtmQmJsdmtyT2ViY2d1M3JDU2tzWlR5YkFldGlPTnlIdjhUR2VZN0FkR2lWZnAvMmxlZGlWbVMxSEVqeFAwWHJESDZDU3NVZTBjQmx6U0lWdGZvdFhkTytiVDd4R2E4RVcxNEdjWEl1cXIybldEVVdXcWlldUdXamhxZk5KZnM1MDYwZW5EUHA3M1dZMkZSa1dmb0JIZ2Z5LzNxbWxyM09EU3o0c0g3OTRYVUNsRjFiRk1YcUM3NVo2NVhRdTBtWm5CanlERS9yNExVUjdlamh4N1A3Wk5iNk43a1pQNEVUaDh3emprdThaQ3VPSWNiSHpKLzMzYmpRaDgxVkdYZGRHTDdpTmtZaE1MUVZOczFqeVdDbFpNdW0wMjlDenFscmFaU013VDB5Mk5ZSzdxTFpFQlBQU05sQUNJZnJ4YVRlcUZ3cGR4LzQxbzVNb0lOMCtmMFhYdDM2WndEWUVNdlRIdis0QnpOWmJuQUVDbnh4M0VtUEY2dzNJNjNUT2lHYjJOUWNnT29TczM2bzhXRXdnQ3plRTRCUStuRWs5RFdBc0w5UlZyTXpKbnNPQWxYWnlqbVBFWHBHVkpQUStvUWprOWEvOVZTWXcxbU9oMHJ0c1RobWFhVSsxVDhhcUpNTTA1OVpoVG9meXBZRFU1KzV4SzRLNHBzcnoiLCJtYWMiOiIyMjcyYjZjZWY2MzA4NTk4NDFkOTBjNjExNzE0MmZkNzA1NzcyMjY4MGViOGZiNzhjZGZlZjVlOTRlZDI1MTI5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNJY0VkQ1BmOVhBUTh0ZFVyQkU0L3c9PSIsInZhbHVlIjoicmltTXhzV29ZMWd6R3lhZFQwa0FxUWZaZVZwKzJqYjVOTFUxaWJ0LzdMa1NZTnNtRlAxcVZBMjlKT095b0pXcVRiaDA5QnRxSExVTFB5d1ppVitPdmxWcGMrRGp5RHhhRVpqY0Z6LzRYRnRTNVJLeUd2WGtGWDVXaktrMFd2Y05EOVVMSFo3eHFOd2ljNTBwVFVFaS9CWk4razd0eXNkYUlkMjNCZHpLS3dNU1p0Y2RJRU9IelAwaTEvZERKL1NJZmpMNEhmekhWdFRpK2JGM3RDWElvQXVxaXlLZVg1Y0d4bTRnb2VsOHBvcm5wRkhsR2tURFd0Z0FSNkhERXBKK2JjaE9kMzh0a0NZVVBIOUFQeUhNdm5Qd3V1M2RUWDJKalZEMXRaMkplMnd3Yk1tZmhvT3d0OVhJZTIwVWtNajZZK01CMWU0RW51Tk9vc29ReDg4NVJ4RUdQYmZYZStNQ3BPdzdDT3pOZUZaR3cyQ1JNQnpnTytwUlZVWFRHOXRobXdCY1NETjkzSlNRK1didHcvM0IrNTFPcGszSS9iK1ZLeUMxY2h4VjJyeEw2TW44OEdPNFIwZnhYamhhcHhKUElSMGVOQ3crWE4xR0tVUWNubFRzRzRjSlRqL1ZTaWN1S2F6VER4WlFKelBVRWJOOStvcTNvNHJkcWNQZjBrMG0iLCJtYWMiOiJlMWVhNzQ5NzMwYTZkYjU3ZjUzNTFlNGRmYTIyNzkwOTdhMGE1NTE2YmUyN2I2YzFiNmU3Nzg5NzlkODExNGI1IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784072412\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1209345422 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qxuNZl2e72jhTUnEQfcLkaI4AHDrGJklMQkQ4RIr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209345422\", {\"maxDepth\":0})</script>\n"}}