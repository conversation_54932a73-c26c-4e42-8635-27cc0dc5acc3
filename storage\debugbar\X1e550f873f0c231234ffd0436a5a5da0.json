{"__meta": {"id": "X1e550f873f0c231234ffd0436a5a5da0", "datetime": "2025-08-02 16:39:54", "utime": **********.419554, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152792.007638, "end": **********.419606, "duration": 2.4119679927825928, "duration_str": "2.41s", "measures": [{"label": "Booting", "start": 1754152792.007638, "relative_start": 0, "end": 1754152793.947633, "relative_end": 1754152793.947633, "duration": 1.9399950504302979, "duration_str": "1.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152793.947658, "relative_start": 1.9400200843811035, "end": **********.419611, "relative_end": 5.0067901611328125e-06, "duration": 0.4719529151916504, "duration_str": "472ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44907128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.009049999999999999, "accumulated_duration_str": "9.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1528442, "duration": 0.00696, "duration_str": "6.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 76.906}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.18037, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 76.906, "width_percent": 23.094}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1203887794 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1203887794\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1920091165 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1920091165\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1691881017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1691881017\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/system-admin/companies?per_page=25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InRLWVNTN2ZUMEtmSUJ0Z2M4TTBCeWc9PSIsInZhbHVlIjoiZForTmRWNkpiYnAwOUUvWStrNkhJZGlabjBPUzdxMU9xT25sV3RXaWY5dmpKZXdpa0FHSHJpdFZDQUF6QjVRS0hVVkVnYUhkYWQzVmdqUUIrdTQ4cTBQWDUvaHJla2xhS0I5ZkhPMHdaTi9nQnpDWHZaQjBsK3VreExZYWZyczh6eTh3Ukhna1BZR1JFcTREWXJlUjVjejFCQW5ncGRPUXBrcUFpOHFIWmtXNEJaTmQ4ZHVBY0pDSVhjUVZEN3lRQ1d1STFZRFg0MngzZkxkazAxVXhlQ0lsVkVvelFOR3BLOXNsY3lhc2o2Y09qVTc0S2ZrOWJqZ1NsVElNVFg1OEZQYUtQMEozS0w4NC96ZWpTTXpIS01Vb1FkeUxJSDJZajBEU3A2VXNOSXRCWjJNYWlHVmVhVVFBYnlqWGlORmtJMFR0Y0lEcHdvUElqOGQwVFNHN2pRSjBlVlc5QXgrWkNwN0RMYWh0ZW1xeFFxbmRvNktKZFFzQjhMbkZmZWw1TXV2aVBUU3crNDBBaWtEdWhOYVFvNnBjR3V1QTQrcEh5UTZvY2FJd1FHMXBucThJZ1graDZEQjJsVkNsRlB1bjlhOFZCN0xjc3haK2tmTmFDbklrcGpvNlZYRFJEWHZrMStSUFVIeFRQUHZhNGVMVEpjVmVsMUQrT3RuL09PK1AiLCJtYWMiOiIyNGRhNzhmZjNjYzZiMjc5OTllNTI0YTgzMGExZDNkZGIzZmQ5MWIwYWYxZjU4NWNjOTlhNTQxZTRiN2YxOTE1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InJabW9OMVdrMVdyL2tXVFVIU1BmbEE9PSIsInZhbHVlIjoibTlYV0tlTXZCbTJzejJYSHowQWx5WFlqK0RIajY4MHBJMmhReU56Ri9wT0RQVkh6eE9hWU9tK1VMV21iOHQ5eDZQc2pSMmFHazdXMnpINE9QWmtCUVNXR3A3YjFOMys1ZDdXem9wK0lMcTVITmlIeVlTWXZKSWdOWlYwOUVETlNGZlB6djlXVFQ0SmRtWlRsbWo3dVYxb2dWWTc1OGU0endRdXErUGVYR3hJL01QdWpWeW5tVDJFQk80K0FtaU1kTkFZcW5GMFdSWXcrc3pmc0Q5ZHRHMW9wa2UwTzR0L2Z0eEY3SWtqbzFONFBHZkVxc0FqUWFBT3JuVmZHT0JDU2JJem04SEN6eis4YXhydGRnQ3c1YXQ0Y0Jrbm9lYlRBZ21xeGRGR25CT0NuaUFSczdBRjNYSUVjcjA2Y3ZLZ0JITzNhUmRWaWdrL095cjE3UVBLZU0zWmlFMVNQaXFUSWR2YnEwK0drWEcwdXNSV1NpUHpZaTQ1TVBhV1g3NXpJNXF4dmRjbFBxS0trSEs4ZktYSVQ0SDAxVENvbVNsR1F6emxOeHRqS0Z0cGRmVDZ1WVA3UEtzUnd0ckZ6Y3FxTGxSbzcwUGRnMVVFZFVFMTJ5aUl1azVCUTZrRkVjTk1YVlFub2xPU3cvZmE4MHBOb2RuUlFiRnRCWXN5VWNCeHEiLCJtYWMiOiJjOGZjODg1OWNhOGFhZTdiMTk4NTc0Mjg1MTQxNDNhNTA5Yjg5ZDM4YmE0NTJkY2U3ZWM5ZjE5Zjc5NzM0YzY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qX0CnOObck67LLGXwnxE1rbA703TvCmAFmaCZvwn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-424987439 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:39:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlUxNFFuVklGc1paMHBVK0Y2SE16VHc9PSIsInZhbHVlIjoiengyaEtNMzJkVnRUdkoxSTlQK2lRQWNKbUJ6b3B6Tk00YXlJaGdQUlpyY0RFb1lwbW5pQ3VLemNIM05CQWNBbEt3MVJjYldhaHdseEt0V09YQTYvU2pPUjNXR0lESGhHRml5b0NTbWR3U1VnZGFGRDBXWnNCQnNac2hlZE1jVWpqWnVxQ1JFZmVJSXJBQUkxQ2NqcWlyajhrdVNuZCs0NTlsMVJzMElQZFkwQll2SFBpUGpjQzVkaS9JUndpSC9IdkRaQ2FlL3h4U3kwQWVXbDVCUUI4dlZuOGJnVXcydWR2OGpWNG5GN3hXbjNnaU1HdDVNK1R4VjNvWkdnVEtlWUFja1N3NFc3QXJzQklYZEJISU1hbFQwYVBJWHB2cEYybFpTK2VOaUJaMGZyQmdvZm9hNWZjOHFuYlo3My9udkhCdXZWN3VyWW1iV1B1U0RldkEvNHl6UFlZUGZqRVQ5R1FZa1gxN0dKZmEwTjV2YmRER1hsUUN5YjltOW9PMFY0bVdvVGh2cE05d1ozV214SUVDdVlDeUp0UWJ2T1RxUTJWMkZNUTQ0aS81UFkxNDR6NjBia0tpUUJNOFpYekNWNis4SGljNHFLNHc4dktmY2h5S053WmFmS1hxZWJkbGZsOXQ1NW1JcTQ4Yk9pdjdVbm5kU1UvT0RxdkhtZHBWTDciLCJtYWMiOiI3ZTdlODU3OTA4MjNmMTUwNzQ0MTBkNTY4ZTkxYTRmYzZmMzJhY2U0MWZjOWE1ZTY4YTVkOGYwZmE4Zjc5ODg0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:39:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNGMElQRUJyZmRBcVFsb0Y4cUhsdUE9PSIsInZhbHVlIjoiZkRNT3ZtSGRaaXVNSUJna1VuWmhHWDFkcUVoVmRsVDBkOW9FVEZWRVEwZkdyVEwxUGljSlNaVDI1YTExMFZWQXg5WEZ4WHZ1U2NqUHFuN09PS0tIYmREUUllQ0s5dzhzVWRDeUNiVnVUc1VyWjJZRVA3YlVidTVDdzFlSzZDMXlEQ3hpQjJ6WVRONTR6clozYlZJc0Y3SERCeGZWeWtYZ21ENjcvZzB3MjFQK0NqcjhONVA4S0huZEJUa0tTMlJ3YXpSRXlZL1pVQzJYNDlWZk1jVWhNTE5pajUrWXJyODdtVkcwSUNhUUJ6MCtZTFpGZWYxcnl5WVYvZGtxcXNqcy9SeWViQUlKeVJwbkNqTjByYWg3TEdXTC9NZURKMTZUcDAyMElpbHhMRmd4TWZSN21QaCt2RnpqL1h1UkU1VmhxR1Ywcjc1SVQ0aUdxNVBHWERzTWU5eU1iL3ZMYUViQWhCWUF6NXFKN1ljZW5TbnhoRlRFUGw5U2NYS2t2N0FINU0zdG0zR2VZYTJadFhERU9YbXMzcGhYNTJlOUErVFV5VWtEd3RXS3hQb2t1dGNUQ25rb0VrSllCWWM3L0tIeC9EUWhxUG9QYXVOTUp3MDBLV3hYTHdaTDJ0Ty9hOTIzUDV3NnhtekVnZ2R2MS83eVhZamdYK0crbUhaSUZGMmsiLCJtYWMiOiJjYzQ3MDkxM2M4MDQ4ZjMyMmRhYWRiZjVkMzU3MWVmY2UxMjE1ODdlZTA0MjYxNmRhNzRkM2YzOGU0NjY2MTM0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:39:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlUxNFFuVklGc1paMHBVK0Y2SE16VHc9PSIsInZhbHVlIjoiengyaEtNMzJkVnRUdkoxSTlQK2lRQWNKbUJ6b3B6Tk00YXlJaGdQUlpyY0RFb1lwbW5pQ3VLemNIM05CQWNBbEt3MVJjYldhaHdseEt0V09YQTYvU2pPUjNXR0lESGhHRml5b0NTbWR3U1VnZGFGRDBXWnNCQnNac2hlZE1jVWpqWnVxQ1JFZmVJSXJBQUkxQ2NqcWlyajhrdVNuZCs0NTlsMVJzMElQZFkwQll2SFBpUGpjQzVkaS9JUndpSC9IdkRaQ2FlL3h4U3kwQWVXbDVCUUI4dlZuOGJnVXcydWR2OGpWNG5GN3hXbjNnaU1HdDVNK1R4VjNvWkdnVEtlWUFja1N3NFc3QXJzQklYZEJISU1hbFQwYVBJWHB2cEYybFpTK2VOaUJaMGZyQmdvZm9hNWZjOHFuYlo3My9udkhCdXZWN3VyWW1iV1B1U0RldkEvNHl6UFlZUGZqRVQ5R1FZa1gxN0dKZmEwTjV2YmRER1hsUUN5YjltOW9PMFY0bVdvVGh2cE05d1ozV214SUVDdVlDeUp0UWJ2T1RxUTJWMkZNUTQ0aS81UFkxNDR6NjBia0tpUUJNOFpYekNWNis4SGljNHFLNHc4dktmY2h5S053WmFmS1hxZWJkbGZsOXQ1NW1JcTQ4Yk9pdjdVbm5kU1UvT0RxdkhtZHBWTDciLCJtYWMiOiI3ZTdlODU3OTA4MjNmMTUwNzQ0MTBkNTY4ZTkxYTRmYzZmMzJhY2U0MWZjOWE1ZTY4YTVkOGYwZmE4Zjc5ODg0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:39:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNGMElQRUJyZmRBcVFsb0Y4cUhsdUE9PSIsInZhbHVlIjoiZkRNT3ZtSGRaaXVNSUJna1VuWmhHWDFkcUVoVmRsVDBkOW9FVEZWRVEwZkdyVEwxUGljSlNaVDI1YTExMFZWQXg5WEZ4WHZ1U2NqUHFuN09PS0tIYmREUUllQ0s5dzhzVWRDeUNiVnVUc1VyWjJZRVA3YlVidTVDdzFlSzZDMXlEQ3hpQjJ6WVRONTR6clozYlZJc0Y3SERCeGZWeWtYZ21ENjcvZzB3MjFQK0NqcjhONVA4S0huZEJUa0tTMlJ3YXpSRXlZL1pVQzJYNDlWZk1jVWhNTE5pajUrWXJyODdtVkcwSUNhUUJ6MCtZTFpGZWYxcnl5WVYvZGtxcXNqcy9SeWViQUlKeVJwbkNqTjByYWg3TEdXTC9NZURKMTZUcDAyMElpbHhMRmd4TWZSN21QaCt2RnpqL1h1UkU1VmhxR1Ywcjc1SVQ0aUdxNVBHWERzTWU5eU1iL3ZMYUViQWhCWUF6NXFKN1ljZW5TbnhoRlRFUGw5U2NYS2t2N0FINU0zdG0zR2VZYTJadFhERU9YbXMzcGhYNTJlOUErVFV5VWtEd3RXS3hQb2t1dGNUQ25rb0VrSllCWWM3L0tIeC9EUWhxUG9QYXVOTUp3MDBLV3hYTHdaTDJ0Ty9hOTIzUDV3NnhtekVnZ2R2MS83eVhZamdYK0crbUhaSUZGMmsiLCJtYWMiOiJjYzQ3MDkxM2M4MDQ4ZjMyMmRhYWRiZjVkMzU3MWVmY2UxMjE1ODdlZTA0MjYxNmRhNzRkM2YzOGU0NjY2MTM0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:39:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424987439\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}