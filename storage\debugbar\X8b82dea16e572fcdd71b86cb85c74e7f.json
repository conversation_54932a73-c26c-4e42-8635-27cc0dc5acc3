{"__meta": {"id": "X8b82dea16e572fcdd71b86cb85c74e7f", "datetime": "2025-08-02 16:37:33", "utime": **********.57158, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152650.47089, "end": **********.571661, "duration": 3.100770950317383, "duration_str": "3.1s", "measures": [{"label": "Booting", "start": 1754152650.47089, "relative_start": 0, "end": **********.375405, "relative_end": **********.375405, "duration": 2.904515027999878, "duration_str": "2.9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.375527, "relative_start": 2.***************, "end": **********.571666, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uoYqk4K00IrDu6dpqwcWSZ6uU7nOrfK3ZoMbnaRe", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1364819579 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1364819579\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1100210846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1100210846\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1421799738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1421799738\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-35328488 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35328488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1123420512 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1123420512\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1637162979 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:37:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVNQWVmcCtMeS9kc1hySFcveUdZRHc9PSIsInZhbHVlIjoiQ0tsR25kTlRpNEt6andUYmlPRUlLT0svVkhUQ0RJSkZOb2ZQYzNJTWJKK3FxOFdleGg0d3B6SjMwaWo2R2VTVzAvdXdmbjFvRVowY3lYWVFVUVNFTGRjcUt6Z0Q1VFNzUldRb3NSQ1gwT0VoWWQxU3Y2dW04ZHJIaEV4T2c4RGVkT1MzSi9rQXhLQ0RUUStDUWFpelFuN3dTcWxOSUJLMjdlMjJqNVdoNVBiOHZkT0Fvd1dxVk9aTG40NGFEZ09jd3RUMEJ1QVJ2SXhBbzZ3bDJrY3J4OEdSWEg1R2tzc3BGZVpoUllOVVRKYUxZcEwwbkd1dytqMW13bGFlVjNFRm04RGllc29Xc2RhZXV1SVowMXJkcnlXRWVmeldPQkUxV25sa3gvR0pzNkszMzBFWkVOSm5UWkNXUTF2QXRCMkI3QUwzbUE4eER6bjc1WW1ybnZ0WTNyVXNXODI3SFNVZEJ0bkNLMGxSeXBsSjFwQmFwTy9MZ3cyODM3ZFhLenMrSUJNNE5mY1BwUW50T1N2QVZqUzZzbXl6eHgvbThMU1RzNnVzeU52bWxFeU5zSGN3aDhJSS9EY1o3ak90anFLMGJUR05GYk1GWVhCWjZUN0h3TXRsMUxpMnlWMURvN2dwYXQ0ZnVKMkdPcFMvTjZQZlNkR3ZZcHZ0d3VyWEtoUDciLCJtYWMiOiIyNjM0NjJlNGIwOTgyZGZiNzM0MzM4NWQwYjQ5MzYyNDRlMjU5OWZmZjVjNDBhMWJkYmRiYjI2YjgxZGI4MWZhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlQyTTJ1aWFMaTJyYnlmTDE4OXYxaGc9PSIsInZhbHVlIjoiQmI4bmRsYk8rNkZaWUkwczM0K2NrMDlyUXI0NDJYTVVWUlpGRll0UG5WM3F4eVdralU5THdEK21NS2hYc2ZuNDVOcUI0Sk15Snkxc25yZGpiVEVURTU2dzM1M3ZwSWxHZmtkTHJnY2VXNGtuOXhHOCtIMUorUjlkQUROakhhOVNhWFYweXFsdE5nWVAvUzJ3bFNSSFA3ZkZYVXY5T1FBNGxEY2lidWhrS2prQWhhMFpMVDA1bkhFRUhhRCs2UTJLdGFCZytqSUdmNFA5TWNSRittc0FMU1c2QW5kOEhndS9Zb3BIejJhd2lta1F3NHYzbnVDbFJCWGhXTEVoV1Rib21ncVF6SXFZTG9FdStrQjB3ZXF0R1dNbnlQamhQMGJDU0t0c1Z3ZytjUS9xS0ovZTJPK1V6cUJCeVhtZXlKNEJPM1hZY1lKUHJhcnErRzNQZnZtRXJBazVScjVHMGhJUCtrOWNtM3VBc3lhZTBtSFUzK0tPQnQ3WXdCNTliSGFwWXJnYzlwbHRPdFM4UWN1ZmtXenZnUFEyYWdIVUFZU1pqWUd3R1V1YmNvcXk4bTdzOHpNdEM4dnhpT2ZFekZFaU8rb0psRXpRbUtFdVk0STRIMWFUTHdwNlFDenpWeFN5cSszd2ZRM3ZUVm1TSHhPcDlEZjdlNW9helJrSnJTTFgiLCJtYWMiOiJjYzA3NTFjZGQ1NzZmODQ0OTJjMGI3YmUxMGNlYTNiZWMxNjM3MDliMWM3MWQ3ZGQ4NjViOTdlYWM1YjE2MzQwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVNQWVmcCtMeS9kc1hySFcveUdZRHc9PSIsInZhbHVlIjoiQ0tsR25kTlRpNEt6andUYmlPRUlLT0svVkhUQ0RJSkZOb2ZQYzNJTWJKK3FxOFdleGg0d3B6SjMwaWo2R2VTVzAvdXdmbjFvRVowY3lYWVFVUVNFTGRjcUt6Z0Q1VFNzUldRb3NSQ1gwT0VoWWQxU3Y2dW04ZHJIaEV4T2c4RGVkT1MzSi9rQXhLQ0RUUStDUWFpelFuN3dTcWxOSUJLMjdlMjJqNVdoNVBiOHZkT0Fvd1dxVk9aTG40NGFEZ09jd3RUMEJ1QVJ2SXhBbzZ3bDJrY3J4OEdSWEg1R2tzc3BGZVpoUllOVVRKYUxZcEwwbkd1dytqMW13bGFlVjNFRm04RGllc29Xc2RhZXV1SVowMXJkcnlXRWVmeldPQkUxV25sa3gvR0pzNkszMzBFWkVOSm5UWkNXUTF2QXRCMkI3QUwzbUE4eER6bjc1WW1ybnZ0WTNyVXNXODI3SFNVZEJ0bkNLMGxSeXBsSjFwQmFwTy9MZ3cyODM3ZFhLenMrSUJNNE5mY1BwUW50T1N2QVZqUzZzbXl6eHgvbThMU1RzNnVzeU52bWxFeU5zSGN3aDhJSS9EY1o3ak90anFLMGJUR05GYk1GWVhCWjZUN0h3TXRsMUxpMnlWMURvN2dwYXQ0ZnVKMkdPcFMvTjZQZlNkR3ZZcHZ0d3VyWEtoUDciLCJtYWMiOiIyNjM0NjJlNGIwOTgyZGZiNzM0MzM4NWQwYjQ5MzYyNDRlMjU5OWZmZjVjNDBhMWJkYmRiYjI2YjgxZGI4MWZhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlQyTTJ1aWFMaTJyYnlmTDE4OXYxaGc9PSIsInZhbHVlIjoiQmI4bmRsYk8rNkZaWUkwczM0K2NrMDlyUXI0NDJYTVVWUlpGRll0UG5WM3F4eVdralU5THdEK21NS2hYc2ZuNDVOcUI0Sk15Snkxc25yZGpiVEVURTU2dzM1M3ZwSWxHZmtkTHJnY2VXNGtuOXhHOCtIMUorUjlkQUROakhhOVNhWFYweXFsdE5nWVAvUzJ3bFNSSFA3ZkZYVXY5T1FBNGxEY2lidWhrS2prQWhhMFpMVDA1bkhFRUhhRCs2UTJLdGFCZytqSUdmNFA5TWNSRittc0FMU1c2QW5kOEhndS9Zb3BIejJhd2lta1F3NHYzbnVDbFJCWGhXTEVoV1Rib21ncVF6SXFZTG9FdStrQjB3ZXF0R1dNbnlQamhQMGJDU0t0c1Z3ZytjUS9xS0ovZTJPK1V6cUJCeVhtZXlKNEJPM1hZY1lKUHJhcnErRzNQZnZtRXJBazVScjVHMGhJUCtrOWNtM3VBc3lhZTBtSFUzK0tPQnQ3WXdCNTliSGFwWXJnYzlwbHRPdFM4UWN1ZmtXenZnUFEyYWdIVUFZU1pqWUd3R1V1YmNvcXk4bTdzOHpNdEM4dnhpT2ZFekZFaU8rb0psRXpRbUtFdVk0STRIMWFUTHdwNlFDenpWeFN5cSszd2ZRM3ZUVm1TSHhPcDlEZjdlNW9helJrSnJTTFgiLCJtYWMiOiJjYzA3NTFjZGQ1NzZmODQ0OTJjMGI3YmUxMGNlYTNiZWMxNjM3MDliMWM3MWQ3ZGQ4NjViOTdlYWM1YjE2MzQwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637162979\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-99998714 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uoYqk4K00IrDu6dpqwcWSZ6uU7nOrfK3ZoMbnaRe</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99998714\", {\"maxDepth\":0})</script>\n"}}