{"__meta": {"id": "X198473eac6064bec121b48d538a617c4", "datetime": "2025-08-02 16:34:28", "utime": 1754152468.003877, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152448.895279, "end": 1754152468.00393, "duration": 19.108651161193848, "duration_str": "19.11s", "measures": [{"label": "Booting", "start": 1754152448.895279, "relative_start": 0, "end": 1754152451.839369, "relative_end": 1754152451.839369, "duration": 2.9440901279449463, "duration_str": "2.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152451.839408, "relative_start": 2.94412899017334, "end": 1754152468.003935, "relative_end": 5.0067901611328125e-06, "duration": 16.16452717781067, "duration_str": "16.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50717440, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": 1754152463.856753, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1754152465.317723, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1754152467.815024, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 6.720400000000001, "accumulated_duration_str": "6.72s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1721249, "duration": 0.01008, "duration_str": "10.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 0.15}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.199712, "duration": 6.69243, "duration_str": "6.69s", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 0.15, "width_percent": 99.584}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.982866, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 99.734, "width_percent": 0.028}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1754152464.634665, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.762, "width_percent": 0.034}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1754152465.854193, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.796, "width_percent": 0.057}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1754152467.544987, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 99.853, "width_percent": 0.074}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1754152467.7857401, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 99.927, "width_percent": 0.046}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1754152467.8012328, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.973, "width_percent": 0.027}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UIXco14JgY2SPjxPhQjeydnazbUfHFjOkYhwk9Nh", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-40585651 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-40585651\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1310435773 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1310435773\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-524945102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-524945102\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-13897195 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImJaQm1PSWpMKzYyc1EzT2pBZ0lpbXc9PSIsInZhbHVlIjoiSHNYN2dLWVlJU3VidFJoTTJmSjJ4L0VYa3hIbTdRL2pwQXk4Kytja0ttemM3Tm5va3YwT1QyNDFieCtKcmVOWWFCclozT1BhTWJVZWhDS3VHcnZGTWpUOFdNYVQxd2N2ZzQvRDdXY2c1Rlg0UlIvQjVKeUY4L1dwVDA5OVkzNDlKZTArd3JSbUd1Qk1BWUVUSVRXRmxIbVgyMGZxZVBUV280TWFPR3Bremd5UTVQaHNLT0NPRGhhUnZCZ0txUE9UY0VxTDhhK0lUL0poTkRDcCtQTnNOKzI0N1U1RU00d3JMTkdjcWJsbk5NcXloTjNQKzNZZ2FLVlNxT3pVTWZPMmNuZ3RHVmpHTHNmVklkK1VPNDVjUDBOWklPN2poTW92dngwZ0VWWWlaZlU3aEwzdnFOcWk4SkJaRTcwRWVzNm9SRnRobzZtcktHcWZodkRKcXBMa1FVYkhESjRvZzBIaVMyREI2VXNkeWFjVzBMdWZGUHhzYmZhODcwcGVwY2F4anFxY1liL1pyRUhjTGZZeWUvcGpoSmY5QU8zVmxWU01NRXI5ekJZYS9qVWZ5b3p0cGFtZzJtb3cvbVBZQVVxdlZVQXllUUJJdVhtOG9icy9HY3prYTJFMU52bXJZMG5xWUM5VlFObmJneVBYZFRPQ3VWaW5iU2xJRnAxUXlJQ1IiLCJtYWMiOiJmMWRmYTA5ODlmZjQ5N2JjMDJkYzkxZDRlM2FhZjJiMTk4NTU0ZDI1MzBmMWU1MzlhZTVhYTRlZTlkOTQyYTQxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InFabnZDcW5sODBrY0oya3NsZ1NXREE9PSIsInZhbHVlIjoiM3F3cG54c0RzeCsvRVY4K1FObU9tSlV4V2hRbTRiSHF6MlJqSG42OTUvU1RkSmtta1YxNXV2NVhkZzJBa055SlJMRTRFeXlNMUlBYTl6SW9UQ0doUG5UWnN4V2dKK1ZWRGlMQ0IwdFhrUXJBemVOL2xYTU81eVhVMTMzTUhOcDZqRC9oTE9xUWJBM0Nuckk3SHpUWG8zZjhxK0JkNmpCR1JYcGZNSGFIbkQvSStLTzdXeEgvTjBqSng3S3llU2hDdVB2MDk3ZlJSZTN5SVBnSEJ0OFdDbzl3TlNNd1hURy9OQkphc1VkSmdpbXo5MUk2eHJmdjM3TlV2ampjbGNYaHBQd3ZrdnBOeWRPeTRPQTFCeFE3SjFDWDQxa0w2ZFp6eVBLc0M2cENkOEk1SW5kdGpnWmFTbmVJMzlDZlFpWlYxWWFpQUlKWUdySEZ4MVVDdGJ1ZmdFUk55YWpPYzhxM01EQytzNHJtRVhzSCtVZnJHcnRSZWUvWkZpYlpBcVR3d1dWL0phb0NtUDJmQVJ5ZjRRSVpia3M5UTcybGNUYmhOYk5RbWhmQnpuOS9lNndYQ2RMRkplSUVaMUI1NXU2VUxDa1ZvQjhqVHpYeGVpSmQvVWU3WmgvSHZ4Z3hJMkZaMVkrUWtRcVNmTmZvdkQ1SkRrQ1BBYWd0UWM5QUdycmMiLCJtYWMiOiI3ZGZiOTBiN2EzNWE1MzQzNTJiYzYzZjNhZWNlMTdlMzQ4YTZiYWE4MDJiNmFiYzA0NjNhNDJlZWFjNzZiYmFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13897195\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1982665720 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UIXco14JgY2SPjxPhQjeydnazbUfHFjOkYhwk9Nh</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jgOHlClirnVlORGHis1rTmVA2UQstIg24my82zNm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982665720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1316331450 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:34:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJ0SHRWNTIzZHRjQUhOQUNUaE54MUE9PSIsInZhbHVlIjoiM002MWVFWDRtL0t0Q2MrZ3lKTzROUjhUc1A0Ym50WFdFS0FSdkxVRDBGdVBOczBpN0VsU1daZnJXOGg1M3dNVkhHM0tIOGxiYjc2djFIVXMySjJKNENQQUU2QnkzdjByVk1oNEhCMUx6UmRNUGp0SFdPVkxPTkYxc2hrVjJKU1hWZ0ppUzVvdTNXa3pRcVVWV1ZjMEdudFZTZStlbC9zcXFyMnU1Z3czSlBjL1pJYjk1RmFNZTRFZGJHdCtLZkVYa0JSZFBlOFBqZ0xxZUtYVEY1VTNoNGN5NFYxRVlnUk1teEN4c3VlaXA1S3p0SUhyVHg4Qm5vd1JFY2dDeW5hbFhLY0lnc3dxVW51TzhxZDdlWnJUTDVpRnNzOVBqUTFVV2czMHBNUVZ1M3ROSXAwZHlTQmdFdEgvT2MyVjJvQWJzN3EweU9mVlRBaXY2YlhnMUU0cnpsZXNKNXJKamtUU1hPL1VSL05XWHhVR0N1MnRlT0FzRkJEYnJoTWtCaDZKRzR3Wmdyc3hYOGVCSUZjcElPOUN4RkR3MnloSWR1eC9MMTJNUVF6NG1aS1lWc1lIcTVqSFpxVk9FY2hjeGZiL3ZnZ24yd0FuZ0JDOEZhZlhhc3U0MEVZRWtGdXV1WDRPSGtnWVYzdFRGb0lZZUtqOFVBcW9NbDIycFpWbUQ1d04iLCJtYWMiOiIwZTQ4ZTYwN2RhMGIwM2QwNjQzNmYwMGJmMDBkYTEyZjNhNGZhZDI0Y2U5MDQ2OGQ1MTYzZTcxZThlZTVhMWNlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:34:27 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFhbFg2VmpXeERMSlFRM01EZkhWL2c9PSIsInZhbHVlIjoiL2d5YUdzVXBGS245ZFAzZmxzQlVGaXFQQytWaCtER0YrQ2pVK0I1UFBXb1A3QjdBeU8vdzFJRFRBNlBwZ1FpbGZiaGlSNW9qK2lHZEdEc1d1OElOWXMrQi9YaTVpWEhEZytvNnhYSE9uUi84ZWswR3Q0M1orZ3NyWjRTcmZ6cDVxaVhnRDkxU2J6T2ZyemVSQ28xZVF4QWd6NC9adHpVZm1vVW9YV1VWZ0FUVVVjQmpEbWFJMEdIbndtQjd5TEQ5bitNTjlEQlEvVzhoNWJ2Slp1Y3lzRGRxSTNhZ3hBVXFGNU5Kb1pReGp4MkZqYlhlNldmQTd2bDA4UlpqYWt0MUdKQnJybGZXQU1pcGd1aDJ3SDdsbUtkazdzQThMNW9hTU1IYWp0aTlJcWcrdEZxa0F4cjBnUmFMbzlYU1VhVG9jZDZ5N3ZUekx4ejJwSGdkbFBFQm1obG5wTGR6b2dMdDd5cDRseHpPTzRHb1NSSnlGWVNXY0p6bGJUQjhpdk5ncm82NEVqWDYvTDZQaGVkblJEN2R0SWx1VytldlpSblRBN2RzTlNHbmFCVnVjL01FV2k4VCtTWURySWVYZ1pteEMzZ2I3WDA0azNHczdrOHUvYi9XUjZ1N3p1dWo0YkNGYy9sSzZKeGJoeGdJcmVmWndMMjhDOGk2dnpQUGxzdjAiLCJtYWMiOiI4ZWYzNTg0NzgxMjk1OWJiM2Y5MzNmMGYwZmE0ZTE5MjI2Y2MwODdlZmFiYzFkZDk5NTU3MDI5OTNkN2E5YTE2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:34:27 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJ0SHRWNTIzZHRjQUhOQUNUaE54MUE9PSIsInZhbHVlIjoiM002MWVFWDRtL0t0Q2MrZ3lKTzROUjhUc1A0Ym50WFdFS0FSdkxVRDBGdVBOczBpN0VsU1daZnJXOGg1M3dNVkhHM0tIOGxiYjc2djFIVXMySjJKNENQQUU2QnkzdjByVk1oNEhCMUx6UmRNUGp0SFdPVkxPTkYxc2hrVjJKU1hWZ0ppUzVvdTNXa3pRcVVWV1ZjMEdudFZTZStlbC9zcXFyMnU1Z3czSlBjL1pJYjk1RmFNZTRFZGJHdCtLZkVYa0JSZFBlOFBqZ0xxZUtYVEY1VTNoNGN5NFYxRVlnUk1teEN4c3VlaXA1S3p0SUhyVHg4Qm5vd1JFY2dDeW5hbFhLY0lnc3dxVW51TzhxZDdlWnJUTDVpRnNzOVBqUTFVV2czMHBNUVZ1M3ROSXAwZHlTQmdFdEgvT2MyVjJvQWJzN3EweU9mVlRBaXY2YlhnMUU0cnpsZXNKNXJKamtUU1hPL1VSL05XWHhVR0N1MnRlT0FzRkJEYnJoTWtCaDZKRzR3Wmdyc3hYOGVCSUZjcElPOUN4RkR3MnloSWR1eC9MMTJNUVF6NG1aS1lWc1lIcTVqSFpxVk9FY2hjeGZiL3ZnZ24yd0FuZ0JDOEZhZlhhc3U0MEVZRWtGdXV1WDRPSGtnWVYzdFRGb0lZZUtqOFVBcW9NbDIycFpWbUQ1d04iLCJtYWMiOiIwZTQ4ZTYwN2RhMGIwM2QwNjQzNmYwMGJmMDBkYTEyZjNhNGZhZDI0Y2U5MDQ2OGQ1MTYzZTcxZThlZTVhMWNlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:34:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFhbFg2VmpXeERMSlFRM01EZkhWL2c9PSIsInZhbHVlIjoiL2d5YUdzVXBGS245ZFAzZmxzQlVGaXFQQytWaCtER0YrQ2pVK0I1UFBXb1A3QjdBeU8vdzFJRFRBNlBwZ1FpbGZiaGlSNW9qK2lHZEdEc1d1OElOWXMrQi9YaTVpWEhEZytvNnhYSE9uUi84ZWswR3Q0M1orZ3NyWjRTcmZ6cDVxaVhnRDkxU2J6T2ZyemVSQ28xZVF4QWd6NC9adHpVZm1vVW9YV1VWZ0FUVVVjQmpEbWFJMEdIbndtQjd5TEQ5bitNTjlEQlEvVzhoNWJ2Slp1Y3lzRGRxSTNhZ3hBVXFGNU5Kb1pReGp4MkZqYlhlNldmQTd2bDA4UlpqYWt0MUdKQnJybGZXQU1pcGd1aDJ3SDdsbUtkazdzQThMNW9hTU1IYWp0aTlJcWcrdEZxa0F4cjBnUmFMbzlYU1VhVG9jZDZ5N3ZUekx4ejJwSGdkbFBFQm1obG5wTGR6b2dMdDd5cDRseHpPTzRHb1NSSnlGWVNXY0p6bGJUQjhpdk5ncm82NEVqWDYvTDZQaGVkblJEN2R0SWx1VytldlpSblRBN2RzTlNHbmFCVnVjL01FV2k4VCtTWURySWVYZ1pteEMzZ2I3WDA0azNHczdrOHUvYi9XUjZ1N3p1dWo0YkNGYy9sSzZKeGJoeGdJcmVmWndMMjhDOGk2dnpQUGxzdjAiLCJtYWMiOiI4ZWYzNTg0NzgxMjk1OWJiM2Y5MzNmMGYwZmE0ZTE5MjI2Y2MwODdlZmFiYzFkZDk5NTU3MDI5OTNkN2E5YTE2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:34:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316331450\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-399148883 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UIXco14JgY2SPjxPhQjeydnazbUfHFjOkYhwk9Nh</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399148883\", {\"maxDepth\":0})</script>\n"}}