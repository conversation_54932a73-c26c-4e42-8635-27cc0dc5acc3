{"__meta": {"id": "X6f63877f2714c16f289acb0715a90018", "datetime": "2025-08-02 16:38:35", "utime": **********.773648, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152712.80954, "end": **********.773686, "duration": 2.9641458988189697, "duration_str": "2.96s", "measures": [{"label": "Booting", "start": 1754152712.80954, "relative_start": 0, "end": **********.563264, "relative_end": **********.563264, "duration": 2.7537238597869873, "duration_str": "2.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.563298, "relative_start": 2.***************, "end": **********.773689, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PBf2DT9Ri1tPw8rugDMZKpKoliQUxHBQNKziOPGl", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-678250910 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-678250910\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-967870002 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-967870002\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47133583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-47133583\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-202219264 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202219264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1704755045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1704755045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1459138610 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUvdGUvQkkyQnI2LzRXMEZpMmlhWFE9PSIsInZhbHVlIjoiaW5zZFlreWNTSEtUZndSaTE3ZnlHZFZPejgxclExWG9QUHdRN0VjZ1RVL2dSSjJJVklUT3UzSEZXcHVEbXBaQ1R4RXMvcnNEdzdWWUNpMFB6QkFnTUJwODB2OGxCeG1kUmNjZVZWazhCUnVIMER0NCtPR1RsUTc4L0tRU3NPeEN1ZlNrb0RZZEtCaC9BOHUxSWdmVmtlcVJ4Z1NSSU80UnhsS2t4dzdBY3NnYVQxSHdzL04xcHp0VkdlQ0NFcVhvaU5jcFdXK09zcnI1Q3lqZ24yd081Q0kwN2QydTVSaW10dkJWQ1JWOFpQa0hkYkVBRWR6S0doMldROUZkaTFPVDFrQlhwbDEyL2FrK0ZHUWpzTlFOOWpmYkhLNkZ3Q2FBZERWNHh5NWlpdXBlMStGc3JHUnE2ZE5aR0VnaTRHejRSZ3l2THNLc2pXbEwwQ2NCWE9QaFpWNEJST0lKdW9FMTluN3pWY05oMHMyUzI2WWJ1bmpHbFA5ckNrb1BYazE4cThaQmlEWUlXTTNpMFZZNUtGSXNGSW1OeWhXMG1MM1FuRzE2djkwZS9SR1pjV0NZSkFpbGRQQTFhQzI5MnUyanNIWDVpa1NJZjRFU3BaUmU1TW1BdDlNMHN5Z0ZnR0ZiaGpORm53ZmV2RzdQekQyWjZTalQ4aVV2MXZ1Q2V1N2UiLCJtYWMiOiIyZjI4NTIzNjhmNDdiNTNjYjMxYTBlMjc2NjEwMzE2MzJlM2RiM2UwZThmMzEyMjBiNTM4MjA4YjJjZjdjYzZhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVNa2NsRE8raG1BUDBvNDBaam51cGc9PSIsInZhbHVlIjoiR3lQM3hRTnY4WndKTVFnMVRSTmFnUk41MDhJYmdteXhLVzhEMWdGWkFRV0VObklQWWVhTXdRRzRidjlTYmdMUmQrQVd3NU1rckVqVVY5MVRlVlN3MndtQXFLOUoyaDdrMkVYc2J5VXlqeHFsUWxoV1VuN0RWTGhGVXZIU0VYK1kzSmdQNVh0M1Fuek11ZElJS21MWUhhQVhoNW1QM0I2Z2hKME45VUZHN2k3aTcyT1grYWl1V1pQZlJLaHZGaEVYTDFGQlg4a3NVYXBxVXh4RitJRXRvaEptUktkdUFoMkl6RkpKRzUxczdsSm9YNnZxY1VZMnFqNW9NZWQvRkNoMFlvaVZweURCdFJFTU9ZclZrVjZMQTM2SmJuUUFibm1KcUdLaHdpSnRHU3l4WTM4cml5M1ZPYWNoVUcveUEvY0VPa1pSZ3UrQVlmTUQxdkZtRjJJWFNZemwraGFCQ2ljaC8rT1VFc1FwNEc2VE1nOXZWV0M5L1hXWXE4QUtwMHYzSURYeEN4Q2JXamxMOG9ZU2pRU25oM1dSNWhUK3AwcXRPL1d6Qm9TOTJNZk9VWHJzZUpXZk9aTjM1aGw3aE14OWNXREJqZnhIQjc1TERuVVlqYWs5dFVNRG9PZlBCdHBDMHhxSW44VXZrc05mWEFWeTZSMGEwcm90RTlNTWtSN08iLCJtYWMiOiI2OWU4YmNhNTA0MmY4Y2ViNDE5NDk2ODY2OWM4YzE1OTFhOTAyMDdlMzk2YjZjN2Y0ZTAxZDBiMDNkYTQ3OTg3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUvdGUvQkkyQnI2LzRXMEZpMmlhWFE9PSIsInZhbHVlIjoiaW5zZFlreWNTSEtUZndSaTE3ZnlHZFZPejgxclExWG9QUHdRN0VjZ1RVL2dSSjJJVklUT3UzSEZXcHVEbXBaQ1R4RXMvcnNEdzdWWUNpMFB6QkFnTUJwODB2OGxCeG1kUmNjZVZWazhCUnVIMER0NCtPR1RsUTc4L0tRU3NPeEN1ZlNrb0RZZEtCaC9BOHUxSWdmVmtlcVJ4Z1NSSU80UnhsS2t4dzdBY3NnYVQxSHdzL04xcHp0VkdlQ0NFcVhvaU5jcFdXK09zcnI1Q3lqZ24yd081Q0kwN2QydTVSaW10dkJWQ1JWOFpQa0hkYkVBRWR6S0doMldROUZkaTFPVDFrQlhwbDEyL2FrK0ZHUWpzTlFOOWpmYkhLNkZ3Q2FBZERWNHh5NWlpdXBlMStGc3JHUnE2ZE5aR0VnaTRHejRSZ3l2THNLc2pXbEwwQ2NCWE9QaFpWNEJST0lKdW9FMTluN3pWY05oMHMyUzI2WWJ1bmpHbFA5ckNrb1BYazE4cThaQmlEWUlXTTNpMFZZNUtGSXNGSW1OeWhXMG1MM1FuRzE2djkwZS9SR1pjV0NZSkFpbGRQQTFhQzI5MnUyanNIWDVpa1NJZjRFU3BaUmU1TW1BdDlNMHN5Z0ZnR0ZiaGpORm53ZmV2RzdQekQyWjZTalQ4aVV2MXZ1Q2V1N2UiLCJtYWMiOiIyZjI4NTIzNjhmNDdiNTNjYjMxYTBlMjc2NjEwMzE2MzJlM2RiM2UwZThmMzEyMjBiNTM4MjA4YjJjZjdjYzZhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVNa2NsRE8raG1BUDBvNDBaam51cGc9PSIsInZhbHVlIjoiR3lQM3hRTnY4WndKTVFnMVRSTmFnUk41MDhJYmdteXhLVzhEMWdGWkFRV0VObklQWWVhTXdRRzRidjlTYmdMUmQrQVd3NU1rckVqVVY5MVRlVlN3MndtQXFLOUoyaDdrMkVYc2J5VXlqeHFsUWxoV1VuN0RWTGhGVXZIU0VYK1kzSmdQNVh0M1Fuek11ZElJS21MWUhhQVhoNW1QM0I2Z2hKME45VUZHN2k3aTcyT1grYWl1V1pQZlJLaHZGaEVYTDFGQlg4a3NVYXBxVXh4RitJRXRvaEptUktkdUFoMkl6RkpKRzUxczdsSm9YNnZxY1VZMnFqNW9NZWQvRkNoMFlvaVZweURCdFJFTU9ZclZrVjZMQTM2SmJuUUFibm1KcUdLaHdpSnRHU3l4WTM4cml5M1ZPYWNoVUcveUEvY0VPa1pSZ3UrQVlmTUQxdkZtRjJJWFNZemwraGFCQ2ljaC8rT1VFc1FwNEc2VE1nOXZWV0M5L1hXWXE4QUtwMHYzSURYeEN4Q2JXamxMOG9ZU2pRU25oM1dSNWhUK3AwcXRPL1d6Qm9TOTJNZk9VWHJzZUpXZk9aTjM1aGw3aE14OWNXREJqZnhIQjc1TERuVVlqYWs5dFVNRG9PZlBCdHBDMHhxSW44VXZrc05mWEFWeTZSMGEwcm90RTlNTWtSN08iLCJtYWMiOiI2OWU4YmNhNTA0MmY4Y2ViNDE5NDk2ODY2OWM4YzE1OTFhOTAyMDdlMzk2YjZjN2Y0ZTAxZDBiMDNkYTQ3OTg3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459138610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1423049060 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PBf2DT9Ri1tPw8rugDMZKpKoliQUxHBQNKziOPGl</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423049060\", {\"maxDepth\":0})</script>\n"}}