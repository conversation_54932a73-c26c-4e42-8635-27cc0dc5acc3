{"__meta": {"id": "X18987462a6c0973b745cf7e0a2f58032", "datetime": "2025-08-02 16:40:10", "utime": **********.710506, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152808.066751, "end": **********.710591, "duration": 2.6438400745391846, "duration_str": "2.64s", "measures": [{"label": "Booting", "start": 1754152808.066751, "relative_start": 0, "end": **********.479854, "relative_end": **********.479854, "duration": 2.4131031036376953, "duration_str": "2.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.479924, "relative_start": 2.***************, "end": **********.710604, "relative_end": 1.2874603271484375e-05, "duration": 0.*****************, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dTZFHaaA7134UlRnlFBuV6n1OOe0q5kihQDWt8Z4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1458020091 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1458020091\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-843398128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-843398128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-490362496 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-490362496\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-614525068 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614525068\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-752412197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-752412197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1488006829 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:40:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJmQ2dQeUwwRjFtT3kzbTdkNUhNMmc9PSIsInZhbHVlIjoiMlRLdSs3R0tVRDQzZUpBc1VtSUdienBDajhMYUk5TnIxTVNyVlJ4MkEzTTVaLzYyN2ZhYVRpeDVIRksyMTlZNmxsVkIvVWpXaVlZSzBOekl0QzZKNHh6WWx0NVpHYWQ3NDlyZ3Vzc0x2L3d6ZGFGZEMwZlNIY2JxNlB2QS9CdTZEMEs2VERmR2dnK0xsbk8zZ09aWGhoT1krUUdvcjlqK0RWM3lYZWljV1dKVHdaK3pIOVI2RTlaYjJYZXJIemhLbFJYdUR0N29VUml3R3hRZGdIUTk0dnBTNW93c1VUVHFXMWxnZVQ4RXlqZXpTK0czVS9vYXRkcnhKaDFSdHVNNCtPUVBwa0FNQ3dsaEJsVDh5NDhEWDhrenF6dmRlQytJeCsvaHVwcTZrSGJWVXhNUWU4OHRnbjdPcHd2RUdLdXp3TlFjN1p2NjE3VXdkYnEvSGhsRmtPSjk4L01SWHhuZkVBSDRPL1BQV0tJbnV1ZUEwbnhYRndqQkVVZHNrUzN2QzhoYUd1OElKUDAvWWRoS3FUcXZJREtpak43R2lxVklXc2xKNjFwZjB1cGRKRDVpdFJxc1g4M0JpeUYweFErVlhFUUNqR251TUJWMUxQRGxQbU1oUGk2a21NeXlCZnJMcDRyaDN3QzFrU29xWE5JK1lEZ2t6RkJYRC9FUjVjUWsiLCJtYWMiOiI1ZjkxOTcyYTZmZjM0NWI2OGZlY2Q4ZWZmYTkxYzRiN2UwMDQwNzhhY2QzN2FhNTU2ZmZhMjk0OTFhMDEwZjBhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:40:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1TL01XZHZ5RnhVT1h1OFRlQzhyY0E9PSIsInZhbHVlIjoibVlqUTFteDh6N2JoMUN5VWI4cEtLYjlLd29SM1FqL25ySGJSNllyZDVOTDRXNTFVWXE1c2QyVXIydUZHN29SbWhkcXUxMmE3c1dielFmRWsvMFJNNGNZZ3Q4Y1B2bDZpcjErR1VwMUlxZnN4enVPNWJxN0Y1aEwweHhQSzFPOFQxeWVVQnpKdU1TajdHUDRoMVRUYThFaFh2cm9ZaDdkdk8xM3ZxRm8xblNZMXJGZFVSOE5QeG83b3VzQUFvSUJRYytCcGkrOVpueXZIMUlURHoyN2Q1L3BRT3V3OGxWYWUwUnhyZVh2L3RNeDNENTlNVkNCVUVCcHZOYnJ0OG1pUUxCYU1NMGRDbmhYd01IcFMwUHJ4Yk9Ja0oyMlpsMk5BaE1HNFZTWnFGUHZPRkI4bkxqVjRmeXZSc284ZVVLTHpZK3l0WjdvcHgzNUdGYmxHWnRJT281ME0xenVHL2R3RExiY0lvY1BGckdhdVVhdVlnS2ZJMmhhbkRuOXVuRitRc1FxbmdLLzh3UkpieEpSY1F6MUw3Q2orWTJPUTJkblVyd3lkL1FrNW9OcjJZK1NtZzRCRjlZNVV2MEhnbFppTWpacndIVTM0MUN1S01FL2dqSzUvUXZpWTZPNkU0V2w0SlNPU0VKbGFNODNKYkRIbXo4d2pTczY4VmdCaWMrbDEiLCJtYWMiOiIwN2M0OTI2NTQ3ZjRjN2I3ZTNjOTE4NjE2YjdlZWU1YzFlMzRlMDFhM2EzZDc5OGE3MDYwY2Q5YmVhYjQzYWMyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:40:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJmQ2dQeUwwRjFtT3kzbTdkNUhNMmc9PSIsInZhbHVlIjoiMlRLdSs3R0tVRDQzZUpBc1VtSUdienBDajhMYUk5TnIxTVNyVlJ4MkEzTTVaLzYyN2ZhYVRpeDVIRksyMTlZNmxsVkIvVWpXaVlZSzBOekl0QzZKNHh6WWx0NVpHYWQ3NDlyZ3Vzc0x2L3d6ZGFGZEMwZlNIY2JxNlB2QS9CdTZEMEs2VERmR2dnK0xsbk8zZ09aWGhoT1krUUdvcjlqK0RWM3lYZWljV1dKVHdaK3pIOVI2RTlaYjJYZXJIemhLbFJYdUR0N29VUml3R3hRZGdIUTk0dnBTNW93c1VUVHFXMWxnZVQ4RXlqZXpTK0czVS9vYXRkcnhKaDFSdHVNNCtPUVBwa0FNQ3dsaEJsVDh5NDhEWDhrenF6dmRlQytJeCsvaHVwcTZrSGJWVXhNUWU4OHRnbjdPcHd2RUdLdXp3TlFjN1p2NjE3VXdkYnEvSGhsRmtPSjk4L01SWHhuZkVBSDRPL1BQV0tJbnV1ZUEwbnhYRndqQkVVZHNrUzN2QzhoYUd1OElKUDAvWWRoS3FUcXZJREtpak43R2lxVklXc2xKNjFwZjB1cGRKRDVpdFJxc1g4M0JpeUYweFErVlhFUUNqR251TUJWMUxQRGxQbU1oUGk2a21NeXlCZnJMcDRyaDN3QzFrU29xWE5JK1lEZ2t6RkJYRC9FUjVjUWsiLCJtYWMiOiI1ZjkxOTcyYTZmZjM0NWI2OGZlY2Q4ZWZmYTkxYzRiN2UwMDQwNzhhY2QzN2FhNTU2ZmZhMjk0OTFhMDEwZjBhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:40:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1TL01XZHZ5RnhVT1h1OFRlQzhyY0E9PSIsInZhbHVlIjoibVlqUTFteDh6N2JoMUN5VWI4cEtLYjlLd29SM1FqL25ySGJSNllyZDVOTDRXNTFVWXE1c2QyVXIydUZHN29SbWhkcXUxMmE3c1dielFmRWsvMFJNNGNZZ3Q4Y1B2bDZpcjErR1VwMUlxZnN4enVPNWJxN0Y1aEwweHhQSzFPOFQxeWVVQnpKdU1TajdHUDRoMVRUYThFaFh2cm9ZaDdkdk8xM3ZxRm8xblNZMXJGZFVSOE5QeG83b3VzQUFvSUJRYytCcGkrOVpueXZIMUlURHoyN2Q1L3BRT3V3OGxWYWUwUnhyZVh2L3RNeDNENTlNVkNCVUVCcHZOYnJ0OG1pUUxCYU1NMGRDbmhYd01IcFMwUHJ4Yk9Ja0oyMlpsMk5BaE1HNFZTWnFGUHZPRkI4bkxqVjRmeXZSc284ZVVLTHpZK3l0WjdvcHgzNUdGYmxHWnRJT281ME0xenVHL2R3RExiY0lvY1BGckdhdVVhdVlnS2ZJMmhhbkRuOXVuRitRc1FxbmdLLzh3UkpieEpSY1F6MUw3Q2orWTJPUTJkblVyd3lkL1FrNW9OcjJZK1NtZzRCRjlZNVV2MEhnbFppTWpacndIVTM0MUN1S01FL2dqSzUvUXZpWTZPNkU0V2w0SlNPU0VKbGFNODNKYkRIbXo4d2pTczY4VmdCaWMrbDEiLCJtYWMiOiIwN2M0OTI2NTQ3ZjRjN2I3ZTNjOTE4NjE2YjdlZWU1YzFlMzRlMDFhM2EzZDc5OGE3MDYwY2Q5YmVhYjQzYWMyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:40:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488006829\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2112717987 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dTZFHaaA7134UlRnlFBuV6n1OOe0q5kihQDWt8Z4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112717987\", {\"maxDepth\":0})</script>\n"}}