
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Dashboard')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
<div class="float-end">
    <a href="#"
       class="btn btn-sm text-white d-flex align-items-center gap-1"
       data-bs-toggle="modal"
       data-bs-target="#dashboardSwitcherModal"
       data-bs-toggle="tooltip"
       title="<?php echo e(__('Change Dashboard')); ?>"
       style="background: linear-gradient(135deg, #1b5e20, #0d47a1); border: none;">
        <i class="ti ti-layout-dashboard"></i>
        <span class="d-none d-md-inline"><i class="ti ti-switch-vertical" style="margin-right: 5px;"></i><?php echo e(__('Switch')); ?></span>
    </a>
</div>

    <!-- Dashboard Switcher Modal -->
    <div class="modal fade" id="dashboardSwitcherModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo e(__('Select Dashboard')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="account">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-wallet fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('Account Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('View financial overview')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="crm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-users fs-1 text-info"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('CRM Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Customer relationship management')); ?></small>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="hrm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-id fs-1 text-warning"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('HRM Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Human resource management')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="pos">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-cash fs-1 text-success"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('POS Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Point of sale system')); ?></small>
                                </div>
                            </div>
                        </div> -->
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="project">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="fas fa-project-diagram fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('Project Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Project management')); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Dashboard Switcher
    document.addEventListener('DOMContentLoaded', function() {
        // Get all dashboard options
        const dashboardOptions = document.querySelectorAll('.dashboard-option');
        
        // Add click event to each option
        dashboardOptions.forEach(option => {
            option.addEventListener('click', function() {
                const dashboardType = this.getAttribute('data-dashboard');
                
                // Store the selected dashboard in localStorage
                localStorage.setItem('selectedDashboard', dashboardType);
                
                // Determine the route based on dashboard type
                let route = '<?php echo e(route("dashboard")); ?>';
                
                switch(dashboardType) {
                    case 'crm':
                        route = '<?php echo e(route("crm.dashboard")); ?>';
                        break;
                    case 'hrm':
                        route = '<?php echo e(route("hrm.dashboard")); ?>';
                        break;
                    case 'pos':
                        route = '<?php echo e(route("pos.dashboard")); ?>';
                        break;
                    case 'project':
                        route = '<?php echo e(route("project.dashboard")); ?>';
                        break;
                        // Default is account dashboard
                }
                
                // Redirect to the selected dashboard
                window.location.href = route;
            });
        });
        
        // Highlight current dashboard in modal when opened
        const modal = document.getElementById('dashboardSwitcherModal');
        if (modal) {
            modal.addEventListener('show.bs.modal', function () {
                const currentPath = window.location.pathname;
                dashboardOptions.forEach(option => {
                    const dashboardType = option.getAttribute('data-dashboard');
                    if (
                        (dashboardType === 'account' && currentPath.includes('account-dashboard')) ||
                        (dashboardType === 'crm' && currentPath.includes('crm-dashboard')) ||
                        (dashboardType === 'hrm' && currentPath.includes('hrm-dashboard')) ||
                        (dashboardType === 'pos' && currentPath.includes('pos-dashboard')) ||
                        (dashboardType === 'project' && currentPath.includes('project-dashboard'))
                    ) {
                        option.classList.add('border-primary');
                    } else {
                        option.classList.remove('border-primary');
                    }
                });
            });
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
<script>
// Chart type options for each chart
const chartTypeOptions = ['pie', 'bar', 'line', 'doughnut', 'horizontalBar'];
const chartTypeLabels = {
    pie: 'Pie',
    bar: 'Bar',
    line: 'Line',
    doughnut: 'Donut',
    horizontalBar: 'Horizontal Bar'
};
// Default chart data for each chart (should match your real data)
const chartDataSets = {
    customerStatusChart: {
        labels: ['Active', 'Inactive', 'Pending'],
        data: [65, 25, 10],
        backgroundColor: ['#2e7d32', '#4caf50', '#81c784']
    },
    invoiceStatusChart: {
        labels: ['Paid', 'Unpaid', 'Partially Paid'],
        data: [50, 30, 20],
        backgroundColor: ['#2E7D32', '#2EB432', '#55D25B']
    },
    billStatusChart: {
        labels: ['Paid', 'Unpaid', 'Partially Paid'],
        data: [40, 45, 15],
        backgroundColor: ['#2E7D32', '#2EB432', '#55D25B']
    },
    cashFlowChart: {
        labels: <?php echo json_encode($incExpLineChartData['day'] ?? ['Mon','Tue','Wed','Thu','Fri','Sat','Sun']); ?>,
        data: <?php echo json_encode($incExpLineChartData['income'] ?? [1000,200,150,300,250,400,350]); ?>,
        backgroundColor: ['#0CAF60']
    },
    incExpBarChart: {
        labels: <?php echo json_encode($incExpBarChartData['month'] ?? ['Jan','Feb','Mar','Apr','May','Jun']); ?>,
        data: <?php echo json_encode($incExpBarChartData['income'] ?? [1200, 1500, 1100, 1800, 1700, 1600]); ?>,
        backgroundColor: ['#0CAF60']
    },
    incomeByCategoryChart: {
        labels: <?php echo json_encode($incomeCategory ?? ['Sales','Services','Other']); ?>,
        data: <?php echo json_encode($incomeCatAmount ?? [5000, 3000, 2000]); ?>,
        backgroundColor: <?php echo json_encode($incomeCategoryColor ?? ['#0CAF60','#2279BD','#81c784']); ?>

    },
    expenseByCategoryChart: {
        labels: <?php echo json_encode($expenseCategory ?? ['Rent','Utilities','Supplies']); ?>,
        data: <?php echo json_encode($expenseCatAmount ?? [2000, 1500, 1000]); ?>,
        backgroundColor: <?php echo json_encode($expenseCategoryColor ?? ['#E53935','#FB8C00','#43A047']); ?>

    }
};

let currentChartKey = null;
let previewChart = null;
let mainCharts = {};

function getStoredChartType(chartKey) {
    return localStorage.getItem('chartType_' + chartKey) || (chartKey === 'customerStatusChart' ? 'doughnut' : 'bar');
}
function setStoredChartType(chartKey, type) {
    localStorage.setItem('chartType_' + chartKey, type);
}

function getChartJsType(type) {
    if(type === 'horizontalBar') return 'bar'; // Chart.js v3+ uses 'bar' with indexAxis: 'y'
    return type;
}
function getChartJsOptions(type) {
    if(type === 'horizontalBar') {
        return {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { position: 'right' } }
        };
    }
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'right' } }
    };
}

function renderChartJs(ctx, chartKey, type) {
    const dataSet = chartDataSets[chartKey];
    // For bar/line with two series (like incExpBarChart), handle as grouped
    let datasets = [{
        data: dataSet.data,
        backgroundColor: dataSet.backgroundColor,
        label: chartKey === 'incExpBarChart' ? 'Income' : undefined
    }];
    if(chartKey === 'incExpBarChart' && Array.isArray(<?php echo json_encode($incExpBarChartData['expense'] ?? []); ?>) && <?php echo json_encode($incExpBarChartData['expense'] ?? []); ?>.length) {
        datasets = [
            {
                label: 'Income',
                data: dataSet.data,
                backgroundColor: dataSet.backgroundColor[0] || '#0CAF60'
            },
            {
                label: 'Expense',
                data: <?php echo json_encode($incExpBarChartData['expense'] ?? [1000, 1200, 900, 1400, 1300, 1250]); ?>,
                backgroundColor: dataSet.backgroundColor[1] || '#2279BD'
            }
        ];
    }
    return new Chart(ctx, {
        type: getChartJsType(type),
        data: {
            labels: dataSet.labels,
            datasets: datasets
        },
        options: getChartJsOptions(type)
    });
}

// Open modal with correct chart type and preview
$(document).on('click', '.chart-settings-btn', function() {
    currentChartKey = $(this).data('chart');
    const savedType = getStoredChartType(currentChartKey);
    $('.chart-type-option').removeClass('active');
    $(`.chart-type-option[data-type="${savedType}"]`).addClass('active');
    // Render preview
    setTimeout(() => {
        if(previewChart) previewChart.destroy();
        const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
        previewChart = renderChartJs(ctx, currentChartKey, savedType);
    }, 200);
});
// Change preview on chart type select
$(document).on('click', '.chart-type-option', function() {
    $('.chart-type-option').removeClass('active');
    $(this).addClass('active');
    const type = $(this).data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type);
});
// Save and update main chart
$('#saveChartTypeBtn').on('click', function() {
    const selectedType = $('.chart-type-option.active').data('type');
    setStoredChartType(currentChartKey, selectedType);
    // Update main chart
    if(mainCharts[currentChartKey]) mainCharts[currentChartKey].destroy();
    const ctx = document.getElementById(currentChartKey).getContext('2d');
    mainCharts[currentChartKey] = renderChartJs(ctx, currentChartKey, selectedType);
    $('#chartSettingsModal').modal('hide');
});
// Initialize all main charts on page load
function initAllMainCharts() {
    Object.keys(chartDataSets).forEach(chartKey => {
        const type = getStoredChartType(chartKey);
        const ctx = document.getElementById(chartKey);
        if(ctx) {
            mainCharts[chartKey] = renderChartJs(ctx.getContext('2d'), chartKey, type);
        }
    });
}
// Load Chart.js if not loaded, then init
if (typeof Chart === 'undefined') {
    var script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
    script.onload = initAllMainCharts;
    document.head.appendChild(script);
} else {
    initAllMainCharts();
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Account')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="row">
            <div class="col-xxl-12">
                <div class="row g-4 mb-4">
                    <!-- Customer Card -->
                    <div class="col-xxl-3 col-md-6 col-12 dash-info-card">
                        <div class="info-card-inner card mb-0 border-0 shadow h-100" style="border: 1px solid #2e7d32; border-radius: 5px; color: #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2);">
                            <div class="info-icon-wrp d-flex flex-column align-items-center px-3 py-3">
                                <div class="d-flex justify-content-between w-100 align-items-center mb-2">
                                    <div class="info-icon" style="background: rgba(46, 125, 50, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-users fa-2x" style="color: #2e7d32;"></i>
                                    </div><h5 style="margin-right: 50px;"><?php echo e(__('Total Customers')); ?></h5>
                                    <h5 class="mb-0 fw-bold"><?php echo e(\Auth::user()->countCustomers()); ?></h5>
                                </div>
                                <a href="<?php echo e(route('customer.index')); ?>"
                                class="text-decoration-none dashboard-link w-40 text-center"
                                style="
                                    display: inline-block;
                                    padding: 8px 16px;
                                    font-size: 14px;
                                    font-weight: 500;
                                    color:rgb(0, 0, 0) !important;
                                    background: #ECF5F3;
                                    border-radius: 999px;
                                    text-decoration: none;
                                    transition: all 0.3s ease-in-out;
                                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                "
                                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 18px rgba(0, 0, 0, 0.25)'"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.15)'"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                >
                                    <?php echo e(__('Details')); ?><i class="fas fa-arrow-circle-up" style="transform: rotate(45deg); margin-left:5px;"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Vendor Card -->
                    <div class="col-xxl-3 col-md-6 col-12 dash-info-card">
                        <div class="info-card-inner card mb-0 border-0 shadow h-100" style="border-radius: 5px; color: #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32;">
                            <div class="info-icon-wrp d-flex flex-column align-items-center px-3 py-3">
                                <div class="d-flex justify-content-between w-100 align-items-center mb-2">
                                    <div class="info-icon" style="background: rgba(46, 125, 50, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-briefcase fa-2x" style="color: #2e7d32;"></i>
                                    </div><h5 style="margin-right: 50px;"><?php echo e(__('Total Vendors')); ?></h5>
                                    <h5 class="mb-0 fw-bold"><?php echo e(\Auth::user()->countVenders()); ?></h5>
                                </div>
                                <a href="<?php echo e(route('vender.index')); ?>"
                                class="text-decoration-none dashboard-link w-40 text-center"
                                style="
                                    display: inline-block;
                                    padding: 8px 16px;
                                    font-size: 14px;
                                    font-weight: 500;
                                    color:rgb(0, 0, 0) !important;
                                    background: #ECF5F3;
                                    border-radius: 999px;
                                    text-decoration: none;
                                    transition: all 0.3s ease-in-out;
                                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                "
                                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 18px rgba(0, 0, 0, 0.25)'"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.15)'"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                >
                                    <?php echo e(__('Details')); ?><i class="fas fa-arrow-circle-up" style="transform: rotate(45deg); margin-left:5px;"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Card -->
                    <div class="col-xxl-3 col-md-6 col-12 dash-info-card">
                        <div class="info-card-inner card mb-0 border-0 shadow h-100" style="border-radius: 5px; color: #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32;">
                            <div class="info-icon-wrp d-flex flex-column align-items-center px-3 py-3">
                                <div class="d-flex justify-content-between w-100 align-items-center mb-2">
                                    <div class="info-icon" style="background: rgba(46, 125, 50, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-file-invoice fa-2x" style="color: #2e7d32;"></i>
                                    </div><h5 style="margin-right: 50px;"><?php echo e(__('Total Invoices')); ?></h5>
                                    <h5 class="mb-0 fw-bold"><?php echo e(\Auth::user()->countInvoices()); ?></h5>
                                </div>
                                <a href="<?php echo e(route('invoice.index')); ?>"
                                class="text-decoration-none dashboard-link w-40 text-center"
                                style="
                                    display: inline-block;
                                    padding: 8px 16px;
                                    font-size: 14px;
                                    font-weight: 500;
                                    color:rgb(0, 0, 0) !important;
                                    background: #ECF5F3;
                                    border-radius: 999px;
                                    text-decoration: none;
                                    transition: all 0.3s ease-in-out;
                                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                "
                                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 18px rgba(0, 0, 0, 0.25)'"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.15)'"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                >
                                    <?php echo e(__('Details')); ?><i class="fas fa-arrow-circle-up" style="transform: rotate(45deg); margin-left:5px;"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Bills Card -->
                    <div class="col-xxl-3 col-md-6 col-12 dash-info-card">
                        <div class="info-card-inner card mb-0 border-0 shadow h-100" style="border-radius: 5px; color: #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32;">
                            <div class="info-icon-wrp d-flex flex-column align-items-center px-3 py-3">
                                <div class="d-flex justify-content-between w-100 align-items-center mb-2">
                                    <div class="info-icon" style="background: rgba(46, 125, 50, 0.1); width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fa fa-file-invoice-dollar fa-2x" style="color: #2e7d32;"></i>
                                    </div><h5 style="margin-right: 75px;"><?php echo e(__('Total Bills')); ?></h5>
                                    <h5 class="mb-0 fw-bold"><?php echo e(\Auth::user()->countBills()); ?></h5>
                                </div>
                                <a href="<?php echo e(route('bill.index')); ?>"
                                class="text-decoration-none dashboard-link w-40 text-center"
                                style="
                                    display: inline-block;
                                    padding: 8px 16px;
                                    font-size: 14px;
                                    font-weight: 500;
                                    color:rgb(0, 0, 0) !important;
                                    background: #ECF5F3;
                                    border-radius: 999px;
                                    text-decoration: none;
                                    transition: all 0.3s ease-in-out;
                                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                "
                                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 18px rgba(0, 0, 0, 0.25)'"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.15)'"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top">
                                    <?php echo e(__('Details')); ?><i class="fas fa-arrow-circle-up" style="transform: rotate(45deg); margin-left:5px;"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pie Charts Section -->
            <div class="row g-4 mb-0">
                <!-- Customers by Status -->
                <div class="col-xxl-4 col-md-6 col-12">
                    <div class="card h-100 border-1 rounded-2 shadow-sm overflow-hidden" 
                        style="border: 1px solid #55CFDB; border-radius: 5px; background: #fff; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                        
                        <div class="card-header bg-transparent border-bottom d-flex justify-content-between align-items-center" style="padding: 1.25rem 1.5rem;">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2" style="color: #2e7d32;"></i><?php echo e(__('Customers by Status')); ?>

                            </h5>
                            <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="customerStatusChart" title="<?php echo e(__('Chart Settings')); ?>">
                                <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                            </button>
                        </div>

                        <div class="card-body">
                            <div style="height: 300px; width: 360px;">
                                <canvas id="customerStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoices by Status -->
                <div class="col-xxl-4 col-md-6 col-12">
                    <div class="card h-100 border-1 rounded-2 shadow-sm overflow-hidden" style="border: 1px solid #55CFDB; border-radius: 5px; background: #fff; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                        <div class="card-header bg-transparent border-bottom d-flex justify-content-between align-items-center" style="padding: 1.25rem 1.5rem;">
                            <h5 class="mb-0"><i class="fas fa-file-invoice me-2" style="color: #2e7d32;"></i><?php echo e(__('Invoices by Status')); ?></h5>
                            <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="invoiceStatusChart" title="<?php echo e(__('Chart Settings')); ?>">
                                <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; width: 400px;">
                                <canvas id="invoiceStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bills by Status -->
                <div class="col-xxl-4 col-md-6 col-12">
                    <div class="card h-100 border-1 rounded-2 shadow-sm overflow-hidden" style="border: 1px solid #55CFDB; border-radius: 5px; background: #fff; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                        <div class="card-header bg-transparent border-bottom d-flex justify-content-between align-items-center" style="padding: 1.25rem 1.5rem;">
                            <h5 class="mb-0"><i class="fas fa-file-invoice-dollar me-2" style="color: #2e7d32;"></i><?php echo e(__('Bills by Status')); ?></h5>
                            <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="billStatusChart" title="<?php echo e(__('Chart Settings')); ?>">
                                <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; width: 400px;">
                                <canvas id="billStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tabs Navigation -->
               <style>
                   .nav-tabs {
                       border-bottom: 2px solid #e9ecef;
                       position: relative;
                   }
                   .nav-tabs .nav-link {
                       color: #000000; /* Changed to black */
                       border: none;
                       border-radius: 4px 4px 0 0;
                       padding: 0.75rem 1.25rem;
                       margin-right: 4px;
                       font-weight: 500;
                       position: relative;
                       overflow: hidden;
                       transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                       background: transparent;
                   }
                   .nav-tabs .nav-link::before {
                       content: '';
                       position: absolute;
                       bottom: 0;
                       left: 0;
                       width: 0;
                       height: 3px;
                       background-color: #2E7D32;
                       transition: width 0.3s ease;
                   }
                   .nav-tabs .nav-link:hover {
                       transform: translateY(-2px);
                       box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                       background: rgba(46, 125, 50, 0.05);
                   }
                   .nav-tabs .nav-link:hover::before {
                       width: 100%;
                   }
                   .nav-tabs .nav-link.active {
                       color: #fff;
                       background:rgb(24, 74, 27);
                       transform: translateY(-2px);
                       box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2);
                       border: none;
                   }
                   .nav-tabs .nav-link.active::before {
                       display: none;
                   }
                   .nav-tabs .nav-link i {
                       transition: transform 0.3s ease;
                       margin-right: 8px;
                   }
                   .nav-tabs .nav-link:hover i {
                       transform: scale(1.1);
                   }
                   .nav-tabs .nav-link.active i {
                       color: #fff !important;
                       transform: scale(1.1);
                   }
               </style>

               <div class="row g-4 mb-4">
                   <!-- Income vs Expense Card -->
                   <div class="col-xxl-4">
                       <div class="card h-100 border border-info rounded-2" style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                           <div class="card-header">
                               <h5 class="mb-0"><i class="fas fa-exchange-alt me-2" style="color: #2E7D32;"></i><?php echo e(__('Income Vs Expense')); ?></h5>
                           </div>
                           <div class="card-body mb-0">
                               <div class="row g-3">
                                   <!-- Income Today -->
                                   <div class="col-md-6">
                                        <div class="card border-0" style="border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                            <div class="p-3">
                                                <!-- Top Row: Icon Left + Amount Right -->
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <i class="fas fa-chart-line fa-2x" style="color:rgb(22, 128, 26);"></i>
                                                    <h6 class="mb-0" style="color:rgb(0, 0, 0);"><?php echo e(\Auth::user()->priceFormat(\Auth::user()->todayIncome())); ?></h6>
                                                </div>

                                                <!-- Centered Title -->
                                                <div class="text-center">
                                                    <h5 class="mb-0 mt-4" style="color:rgb(0, 0, 0);"><?php echo e(__('Income Today')); ?></h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                   <!-- Expense Today -->
                                   <div class="col-md-6">
                                        <div class="card border-0" style="border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                            <div class="p-3">
                                                <!-- Top Row: Icon Left + Amount Right -->
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <i class="fas fa-chart-line fa-2x" style="color: #16801A;"></i>
                                                    <h6 class="mb-0" style="color: #212121;"><?php echo e(\Auth::user()->priceFormat(\Auth::user()->todayExpense())); ?></h6>
                                                </div>

                                                <!-- Centered Title -->
                                                <div class="text-center">
                                                    <h5 class="mb-0 mt-4" style="color: #212121;"><?php echo e(__('Expense Today')); ?></h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                   <!-- Income This Month -->
                                   <div class="col-md-6">   
                                        <div class="card border-0" style="border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                            <div class="p-3">
                                                <!-- Top Row: Icon Left + Amount Right -->
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <i class="fas fa-chart-line fa-2x" style="color: #16801A;"></i>
                                                    <h6 class="mb-0" style="color: #212121;"><?php echo e(\Auth::user()->priceFormat(\Auth::user()->incomeCurrentMonth())); ?></h6>
                                                </div>

                                                <!-- Centered Title -->
                                                <div class="text-center">
                                                    <h5 class="mb-0 mt-4" style="color: #212121;"><?php echo e(__('Income Month')); ?></h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                   <!-- Expense This Month -->
                                   <div class="col-md-6">
                                        <div class="card border-0" style="border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                            <div class="p-3">
                                                <!-- Top Row: Icon Left + Amount Right -->
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <i class="fas fa-chart-line fa-2x" style="color: #16801A;"></i>
                                                    <h6 class="mb-0" style="color: #212121;"><?php echo e(\Auth::user()->priceFormat(\Auth::user()->expenseCurrentMonth())); ?></h6>
                                                </div>

                                                <!-- Centered Title -->
                                                <div class="text-center">
                                                    <h5 class="mb-0 mt-4" style="color: #212121;"><?php echo e(__('Expense Month')); ?></h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                               </div>
                           </div>
                       </div>
                   </div>

                   <!-- Invoices Card -->
                   <div class="col-xxl-4">
                       <div class="card h-100 border border-info rounded-2" style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                           <div class="card-header">
                               <div class="d-flex justify-content-between align-items-center mb-3">
                                   <h5 class="mb-0"><i class="fas fa-file-invoice me-2" style="color:rgb(11, 83, 20);"></i><?php echo e(__('Invoices')); ?></h5>
                                   <ul class="nav nav-pills" id="invoiceTab" role="tablist">
                                       <li class="nav-item" role="presentation">
                                           <button class="nav-link active btn-sm" id="weekly-invoice-tab" data-bs-toggle="pill" data-bs-target="#weekly-invoice" type="button" role="tab" aria-controls="weekly-invoice" aria-selected="true"><?php echo e(__('Weekly')); ?></button>
                                       </li>
                                       <li class="nav-item" role="presentation">
                                           <button class="nav-link btn-sm" id="monthly-invoice-tab" data-bs-toggle="pill" data-bs-target="#monthly-invoice" type="button" role="tab" aria-controls="monthly-invoice" aria-selected="false"><?php echo e(__('Monthly')); ?></button>
                                       </li>
                                   </ul>
                               </div>
                               <div class="tab-content" id="invoiceTabContent" style="margin-top: 60px;">
                                        <div class="tab-pane fade show active" id="weekly-invoice" role="tabpanel" aria-labelledby="weekly-invoice-tab">
                                            <div class="table-responsive">
                                                <table class="table table-borderless">
                                                    <tbody>
                                                        <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                            <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                                <div>
                                                                    <h6 class="mb-0" style="color: rgb(4, 2, 2);"><?php echo e(__('Total Generated')); ?></h6>
                                                                </div>
                                                                <div class="text-end">
                                                                    <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                        <?php echo e(\Auth::user()->priceFormat($weeklyInvoice['invoiceTotal'])); ?>

                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                            <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                                <div>
                                                                    <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Paid')); ?></h6>
                                                                </div>
                                                                <div class="text-end">
                                                                    <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                        <?php echo e(\Auth::user()->priceFormat($weeklyInvoice['invoicePaid'])); ?>

                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                            <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                                <div>
                                                                    <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Due')); ?></h6>
                                                                </div>
                                                                <div class="text-end">
                                                                    <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                        <?php echo e(\Auth::user()->priceFormat($weeklyInvoice['invoiceDue'])); ?>

                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="monthly-invoice" role="tabpanel" aria-labelledby="monthly-invoice-tab">
                                            <div class="table-responsive">
                                                <table class="table table-borderless">
                                                    <tbody>
                                                        <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px; margin-left: 15px; margin-right: 15px;">
                                                            <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                                <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Generated')); ?></h6>
                                                                <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($monthlyInvoice['invoiceTotal'])); ?>

                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                            <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                                <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Paid')); ?></h6>
                                                                <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($monthlyInvoice['invoicePaid'])); ?>

                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                            <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                                <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Due')); ?></h6>
                                                                <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($monthlyInvoice['invoiceDue'])); ?>

                                                                </span>
                                                            </div>
                                                        </div>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xxl-4">
                            <div class="card h-100 border border-info rounded-2"
                            style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1); border-top: 4px solid #2E7D32;">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center mb-4">
                                        <h5 class="mb-0"><i class="fas fa-file-invoice-dollar me-2" style="color:rgb(18, 110, 16);"></i><?php echo e(__('Bills')); ?></h5>
                                        <ul class="nav nav-pills" id="billsTab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="weekly-bills-tab" data-bs-toggle="pill" data-bs-target="#weekly-bills" type="button" role="tab" aria-controls="weekly-bills" aria-selected="true"><?php echo e(__('Weekly')); ?></button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="monthly-bills-tab" data-bs-toggle="pill" data-bs-target="#monthly-bills" type="button" role="tab" aria-controls="monthly-bills" aria-selected="false"><?php echo e(__('Monthly')); ?></button>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content" id="billsTabContent" style="margin-top: 60px;">
                                    <div class="tab-pane fade show active" id="weekly-bills" role="tabpanel" aria-labelledby="weekly-bills-tab">
                                        <div class="table-responsive">
                                            <table class="table table-borderless">
                                                <tbody>
                                                    <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                        <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                            <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Generated')); ?></h6>
                                                            <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($weeklyBill['billTotal'])); ?>

                                                                </span>                                                                
                                                            </h5>
                                                        </div>
                                                    </div>
                                                    <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                        <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                            <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Paid')); ?></h6>
                                                            <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($weeklyBill['billPaid'])); ?>

                                                                </span>
                                                        </div>
                                                    </div>
                                                    <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                        <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                            <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Due')); ?></h6>
                                                            <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($weeklyBill['billDue'])); ?>

                                                                </span>
                                                        </div>
                                                    </div>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="monthly-bills" role="tabpanel" aria-labelledby="monthly-bills-tab">
                                        <div class="table-responsive">
                                            <table class="table table-borderless">
                                                <tbody>
                                                    <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                        <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                            <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Generated')); ?></h6>
                                                            <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($monthlyBill['billTotal'])); ?>

                                                                </span>
                                                        </div>
                                                    </div>
                                                    <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                        <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                            <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Paid')); ?></h6>
                                                            <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($monthlyBill['billPaid'])); ?>

                                                                </span>
                                                        </div>
                                                    </div>
                                                    <div class="card mb-3 shadow-sm" style="border: 1px solid #e0e0e0; border-radius: 5px;margin-left: 15px; margin-right: 15px;">
                                                        <div class="card-body d-flex justify-content-between align-items-center p-3">
                                                            <h6 class="mb-0" style="color: rgb(0, 0, 0);"><?php echo e(__('Total Due')); ?></h6>
                                                            <span style="
                                                                        background-color: rgba(46, 125, 50, 0.08); /* light green background */
                                                                        color: #2e7d32;                           /* dark green text */
                                                                        padding: 4px 12px;
                                                                        border-radius: 999px;                    /* pill shape */
                                                                        font-weight: 600;
                                                                        font-size: 16px;
                                                                        display: inline-block;
                                                                        min-width: 100px;
                                                                        text-align: center;
                                                                    ">
                                                                    <?php echo e(\Auth::user()->priceFormat($monthlyBill['billDue'])); ?>

                                                                </span>
                                                        </div>
                                                    </div>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
               

               <!-- Initialize Bootstrap Tabs and Tooltips -->
               <?php $__env->startPush('script'); ?>
               <script>
                   document.addEventListener('DOMContentLoaded', function() {
                       // Initialize tooltips
                       var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                       var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                           return new bootstrap.Tooltip(tooltipTriggerEl, {
                               trigger: 'hover',
                               animation: true
                           });
                       });
                       
                       // Activate first tab by default
                       var firstTabEl = document.querySelector('#accountTabs .nav-link:first-child');
                       var firstTab = new bootstrap.Tab(firstTabEl);
                       firstTab.show();
                       
                       // Add ripple effect to tab buttons
                       document.querySelectorAll('.nav-link').forEach(link => {
                           link.addEventListener('click', function(e) {
                               // Remove active class from all tabs
                               document.querySelectorAll('.nav-link').forEach(tab => {
                                   tab.classList.remove('active');
                               });
                               // Add active class to clicked tab
                               this.classList.add('active');
                               
                               // Add ripple effect
                               const rect = this.getBoundingClientRect();
                               const x = e.clientX - rect.left;
                               const y = e.clientY - rect.top;
                               
                               const ripple = document.createElement('span');
                               ripple.style.left = x + 'px';
                               ripple.style.top = y + 'px';
                               ripple.classList.add('ripple-effect');
                               
                               this.appendChild(ripple);
                               
                               // Remove ripple after animation
                               setTimeout(() => {
                                   ripple.remove();
                               }, 1000);
                           });
                       });
                       
                       // Store the active tab in localStorage
                       $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
                           localStorage.setItem('lastAccountTab', $(e.target).attr('data-bs-target'));
                       });
                       
                       // Retrieve active tab from localStorage
                       var lastTab = localStorage.getItem('lastAccountTab');
                       if (lastTab) {
                           const tab = document.querySelector(`button[data-bs-target="${lastTab}"]`);
                           if (tab) {
                               new bootstrap.Tab(tab).show();
                           }
                       }
                   });
               </script>
               
               <?php $__env->stopPush(); ?>
               </div>
                <div class="col-xxl-12">
                    <div class="row g-4 mb-4">
                        <!-- Cashflow Card -->
                        <div class="col-xxl-6">
                            <div class="card h-100 border border-info rounded-3" style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-exchange-alt" style="color: #2e7d32; font-size: 1.3rem;"></i><?php echo e(__(' Cashflow')); ?></h5>
                                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="cashFlowChart" title="<?php echo e(__('Chart Settings')); ?>">
                                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div style="height: 320px; width: 100%;">
                                        <canvas id="cashFlowChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Income & Expense Card -->
                        <div class="col-xxl-6">
                            <div class="card h-100 border border-info rounded-3" style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-chart-line" style="font-size: 1.4rem; color: #2e7d32;"></i><?php echo e(__(' Income & Expense')); ?>

                                    </h5>
                                    <span class="float-end text-muted"><?php echo e(__('Current Year').' - '.$currentYear); ?></span>
                                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="incExpBarChart" title="<?php echo e(__('Chart Settings')); ?>">
                                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div style="height: 320px; width: 100%;">
                                        <canvas id="incExpBarChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Income By Category Card -->
                        <div class="col-xxl-6">
                            <div class="card h-100 border border-info rounded-3" style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-chart-pie" style="color: #2e7d32; font-size: 1.3rem;"></i><?php echo e(__(' Income By Category')); ?>

                                        
                                    </h5>
                                    <span class="float-end text-muted"><?php echo e(__('Year').' - '.$currentYear); ?></span>
                                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="incomeByCategoryChart" title="<?php echo e(__('Chart Settings')); ?>">
                                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div style="height: 320px; width: 100%;">
                                        <canvas id="incomeByCategoryChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Expense By Category Card -->
                        <div class="col-xxl-6">
                            <div class="card h-100 border border-info rounded-3" style="width: 100%; border-color: #b3e5fc; box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-chart-pie" style="color: #2e7d32; font-size: 1.3rem;"></i><?php echo e(__(' Expense By Category')); ?>

                                    </h5>
                                    <span class="float-end text-muted"><?php echo e(__('Year').' - '.$currentYear); ?></span>
                                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="expenseByCategoryChart" title="<?php echo e(__('Chart Settings')); ?>">
                                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div style="height: 320px; width: 100%;">
                                        <canvas id="expenseByCategoryChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-center">
                    <ul class="nav nav-tabs mb-4" id="accountTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link text-black" id="balance-tab" data-bs-toggle="tab" data-bs-target="#balance" type="button" role="tab" aria-controls="balance" aria-selected="true">
                                <i class="fas fa-file-invoice-dollar me-2" style="color:#2E7D32;"></i><?php echo e(__('Account Balance')); ?>

                            </button>
                        </li>
                        <li class="nav-item" role="presentation" style="margin-right: 6px;">
                            <button class="nav-link text-black" id="income-tab" data-bs-toggle="tab" data-bs-target="#income" type="button" role="tab" aria-controls="income" aria-selected="false">
                                <i class="fas fa-coins me-2" style="color:#2E7D32;"></i><?php echo e(__('Latest Income')); ?>

                            </button>
                        </li>
                        <li class="nav-item" role="presentation" style="margin-right: 6px;">
                            <button class="nav-link text-black" id="expense-tab" data-bs-toggle="tab" data-bs-target="#expense" type="button" role="tab" aria-controls="expense" aria-selected="false">
                                <i class="fas fa-receipt me-2" style="color:#2E7D32;"></i><?php echo e(__('Latest Expense')); ?>

                            </button>
                        </li>
                        <li class="nav-item" role="presentation" style="margin-right: 6px;">
                            <button class="nav-link text-black" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab" aria-controls="invoices" aria-selected="false">
                                <i class="fas fa-file-invoice-dollar me-2" style="color:#2E7D32;"></i><?php echo e(__('Recent Invoices')); ?>

                            </button>
                        </li>
                        <li class="nav-item" role="presentation" style="margin-right: 6px;">
                            <button class="nav-link text-black" id="bills-tab" data-bs-toggle="tab" data-bs-target="#bills" type="button" role="tab" aria-controls="bills" aria-selected="false">
                                <i class="fas fa-calculator me-2" style="color:#2E7D32;"></i><?php echo e(__('Recent Bills')); ?>

                            </button>
                        </li>
                    </ul>
               </div>
               <!-- Tab Content -->
               <div class="tab-content mb-5" id="accountTabsContent">
                   <!-- Account Balance Tab -->
                   <div class="tab-pane fade show active" id="balance" role="tabpanel" aria-labelledby="balance-tab">
                       <div class="card" style="border-top: 4px solid #2e7d32;">
                           <div class="card-body">
                               <div class="table-responsive">
                                   <table class="table">
                                       <thead>
                                           <tr>
                                               <th><?php echo e(__('Bank')); ?></th>
                                               <th><?php echo e(__('Holder Name')); ?></th>
                                               <th><?php echo e(__('Balance')); ?></th>
                                           </tr>
                                       </thead>
                                       <tbody>
                                           <?php $__empty_1 = true; $__currentLoopData = $bankAccountDetail; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bankAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                               <tr class="font-style">
                                                   <td><?php echo e($bankAccount->bank_name); ?></td>
                                                   <td><?php echo e($bankAccount->holder_name); ?></td>
                                                   <td><?php echo e(\Auth::user()->priceFormat($bankAccount->opening_balance)); ?></td>
                                               </tr>
                                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                               <tr>
                                                   <td colspan="4">
                                                       <div class="text-center">
                                                           <h6><?php echo e(__('There is no account balance')); ?></h6>
                                                       </div>
                                                   </td>
                                               </tr>
                                           <?php endif; ?>
                                       </tbody>
                                   </table>
                               </div>
                           </div>
                       </div>
                   </div>
                   
                   <!-- Latest Income Tab -->
                   <div class="tab-pane fade" id="income" role="tabpanel" aria-labelledby="income-tab">
                       <div class="card" style="border-top: 4px solid #2e7d32;">
                           <div class="card-body">
                               <div class="table-responsive">
                                   <table class="table">
                                       <thead>
                                           <tr>
                                               <th><?php echo e(__('Date')); ?></th>
                                               <th><?php echo e(__('Customer')); ?></th>
                                               <th><?php echo e(__('Amount Due')); ?></th>
                                           </tr>
                                       </thead>
                                       <tbody>
                                           <?php $__empty_1 = true; $__currentLoopData = $latestIncome; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $income): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                               <tr>
                                                   <td><?php echo e(\Auth::user()->dateFormat($income->date)); ?></td>
                                                   <td><?php echo e(!empty($income->customer)?$income->customer->name:'-'); ?></td>
                                                   <td><?php echo e(\Auth::user()->priceFormat($income->amount)); ?></td>
                                               </tr>
                                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                               <tr>
                                                   <td colspan="4">
                                                       <div class="text-center">
                                                           <h6><?php echo e(__('There is no latest income')); ?></h6>
                                                       </div>
                                                   </td>
                                               </tr>
                                           <?php endif; ?>
                                       </tbody>
                                   </table>
                               </div>
                           </div>
                       </div>
                   </div>
                   
                   <!-- Latest Expense Tab -->
                   <div class="tab-pane fade" id="expense" role="tabpanel" aria-labelledby="expense-tab">
                       <div class="card" style="border-top: 4px solid #2e7d32;">
                           <div class="card-body">
                               <div class="table-responsive">
                                   <table class="table">
                                       <thead>
                                           <tr>
                                               <th><?php echo e(__('Date')); ?></th>
                                               <th><?php echo e(__('Vendor')); ?></th>
                                               <th><?php echo e(__('Amount Due')); ?></th>
                                           </tr>
                                       </thead>
                                       <tbody>
                                           <?php $__empty_1 = true; $__currentLoopData = $latestExpense; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                               <tr>
                                                   <td><?php echo e(\Auth::user()->dateFormat($expense->date)); ?></td>
                                                   <td><?php echo e(!empty($expense->vender)?$expense->vender->name:'-'); ?></td>
                                                   <td><?php echo e(\Auth::user()->priceFormat($expense->amount)); ?></td>
                                               </tr>
                                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                               <tr>
                                                   <td colspan="4">
                                                       <div class="text-center">
                                                           <h6><?php echo e(__('There is no latest expense')); ?></h6>
                                                       </div>
                                                   </td>
                                               </tr>
                                           <?php endif; ?>
                                       </tbody>
                                   </table>
                               </div>
                           </div>
                       </div>
                   </div>
                   
                   <!-- Recent Invoices Tab -->
                   <div class="tab-pane fade" id="invoices" role="tabpanel" aria-labelledby="invoices-tab">
                       <div class="card" style="border-top: 4px solid #2e7d32;">
                           <div class="card-body">
                               <div class="table-responsive">
                                   <table class="table">
                                       <thead>
                                           <tr>
                                               <th>#</th>
                                               <th><?php echo e(__('Customer')); ?></th>
                                               <th><?php echo e(__('Issue Date')); ?></th>
                                               <th><?php echo e(__('Due Date')); ?></th>
                                               <th><?php echo e(__('Amount')); ?></th>
                                               <th><?php echo e(__('Status')); ?></th>
                                           </tr>
                                       </thead>
                                       <tbody>
                                           <?php $__empty_1 = true; $__currentLoopData = $recentInvoice; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                               <tr>
                                                   <td><?php echo e(\Auth::user()->invoiceNumberFormat($invoice->invoice_id)); ?></td>
                                                   <td><?php echo e(!empty($invoice->customer_name)? $invoice->customer_name:''); ?> </td>
                                                   <td><?php echo e(Auth::user()->dateFormat($invoice->issue_date)); ?></td>
                                                   <td><?php echo e(Auth::user()->dateFormat($invoice->due_date)); ?></td>
                                                   <td><?php echo e(\Auth::user()->priceFormat($invoice->getTotal())); ?></td>
                                                   <td>
                                                       <?php if($invoice->status == 0): ?>
                                                           <span class="p-2 px-3 rounded badge status_badge bg-secondary"><?php echo e(__(\App\Models\Invoice::$statues[$invoice->status])); ?></span>
                                                       <?php elseif($invoice->status == 1): ?>
                                                           <span class="p-2 px-3 rounded badge status_badge bg-warning"><?php echo e(__(\App\Models\Invoice::$statues[$invoice->status])); ?></span>
                                                       <?php elseif($invoice->status == 2): ?>
                                                           <span class="p-2 px-3 rounded badge status_badge bg-danger"><?php echo e(__(\App\Models\Invoice::$statues[$invoice->status])); ?></span>
                                                       <?php elseif($invoice->status == 3): ?>
                                                           <span class="p-2 px-3 rounded badge status_badge bg-info"><?php echo e(__(\App\Models\Invoice::$statues[$invoice->status])); ?></span>
                                                       <?php elseif($invoice->status == 4): ?>
                                                           <span class="p-2 px-3 rounded badge status_badge bg-primary"><?php echo e(__(\App\Models\Invoice::$statues[$invoice->status])); ?></span>
                                                       <?php endif; ?>
                                                   </td>
                                               </tr>
                                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                               <tr>
                                                   <td colspan="6">
                                                       <div class="text-center">
                                                           <h6><?php echo e(__('There is no recent invoice')); ?></h6>
                                                       </div>
                                                   </td>
                                               </tr>
                                           <?php endif; ?>
                                       </tbody>
                                   </table>
                               </div>
                           </div>
                       </div>
                   </div>
                   
                   <!-- Recent Bills Tab -->
                   <div class="tab-pane fade" id="bills" role="tabpanel" aria-labelledby="bills-tab">
                       <div class="card" style="border-top: 4px solid #2e7d32;">
                           <div class="card-body">
                               <div class="table-responsive">
                                   <table class="table">
                                       <thead>
                                           <tr>
                                               <th>#</th>
                                               <th><?php echo e(__('Vendor')); ?></th>
                                               <th><?php echo e(__('Bill Date')); ?></th>
                                               <th><?php echo e(__('Due Date')); ?></th>
                                               <th><?php echo e(__('Amount')); ?></th>
                                               <th><?php echo e(__('Status')); ?></th>
                                           </tr>
                                       </thead>
                                       <tbody>
                                           <?php $__empty_1 = true; $__currentLoopData = $recentBill; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                               <tr>
                                                   <td><?php echo e(\Auth::user()->billNumberFormat($bill->bill_id)); ?></td>
                                                   <td><?php echo e(!empty($bill->vender_name)? $bill->vender_name : '-'); ?> </td>
                                                   <td><?php echo e(Auth::user()->dateFormat($bill->bill_date)); ?></td>
                                                   <td><?php echo e(Auth::user()->dateFormat($bill->due_date)); ?></td>
                                                   <td><?php echo e(\Auth::user()->priceFormat($bill->getTotal())); ?></td>
                                                   <td>
                                                       <?php if($bill->status == 0): ?>
                                                           <span class="p-2 px-3 status_badge rounded badge bg-secondary"><?php echo e(__(\App\Models\Bill::$statues[$bill->status])); ?></span>
                                                       <?php elseif($bill->status == 1): ?>
                                                           <span class="p-2 px-3 status_badge rounded badge bg-warning"><?php echo e(__(\App\Models\Bill::$statues[$bill->status])); ?></span>
                                                       <?php elseif($bill->status == 2): ?>
                                                           <span class="p-2 px-3 status_badge rounded badge bg-danger"><?php echo e(__(\App\Models\Bill::$statues[$bill->status])); ?></span>
                                                       <?php elseif($bill->status == 3): ?>
                                                           <span class="p-2 px-3 status_badge rounded badge bg-info"><?php echo e(__(\App\Models\Bill::$statues[$bill->status])); ?></span>
                                                       <?php elseif($bill->status == 4): ?>
                                                           <span class="p-2 px-3 status_badge rounded badge bg-primary"><?php echo e(__(\App\Models\Bill::$statues[$bill->status])); ?></span>
                                                       <?php endif; ?>
                                                   </td>
                                               </tr>
                                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                               <tr>
                                                   <td colspan="6">
                                                       <div class="text-center">
                                                           <h6><?php echo e(__('There is no recent bill')); ?></h6>
                                                       </div>
                                                   </td>
                                               </tr>
                                           <?php endif; ?>
                                       </tbody>
                                   </table>
                               </div>
                           </div>
                       </div>
                   </div>
               </div>

                <div class="col-xxl-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><?php echo e(__('Goal')); ?></h5>
                        </div>
                        <div class="card-body">
                            <?php $__empty_1 = true; $__currentLoopData = $goals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $goal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php
                                    $total= $goal->target($goal->type,$goal->from,$goal->to,$goal->amount)['total'];
                                    $percentage=$goal->target($goal->type,$goal->from,$goal->to,$goal->amount)['percentage'];
                                    $per=number_format($goal->target($goal->type,$goal->from,$goal->to,$goal->amount)['percentage'], Utility::getValByName('decimal_number'), '.', '');
                                ?>
                                <div class="card border-success border-2 border-bottom-0 border-start-0 border-end-0">
                                    <div class="card-body">
                                        <div class="form-check p-0">
                                            <label class="form-check-label d-block" for="customCheckdef1">
                                                <span>
                                                    <span class="row align-items-center">
                                                        <span class="col">
                                                            <span class="text-muted text-sm d-block mb-1"><?php echo e(__('Name')); ?></span>
                                                            <h6 class="text-nowrap mb-3 mb-sm-0"><?php echo e($goal->name); ?></h6>
                                                        </span>
                                                        <span class="col">
                                                            <span class="text-muted text-sm d-block mb-1"><?php echo e(__('Type')); ?></span>
                                                            <h6 class="mb-3 mb-sm-0"><?php echo e(__(\App\Models\Goal::$goalType[$goal->type])); ?></h6>
                                                        </span>
                                                        <span class="col">
                                                            <span class="text-muted text-sm d-block mb-1"><?php echo e(__('Duration')); ?></span>
                                                            <h6 class="mb-3 mb-sm-0"><?php echo e($goal->from .' To '.$goal->to); ?></h6>
                                                        </span>
                                                        <span class="col">
                                                            <span class="text-muted text-sm d-block mb-1"><?php echo e(__('Target')); ?></span>
                                                            <h6 class="mb-3 mb-sm-0"><?php echo e(\Auth::user()->priceFormat($total).' of '. \Auth::user()->priceFormat($goal->amount)); ?></h6>
                                                        </span>
                                                        <span class="col">
                                                            <span class="text-muted text-sm d-block mb-1"><?php echo e(__('Progress')); ?></span>
                                                            <h6 class="mb-2 d-block"><?php echo e(number_format($goal->target($goal->type,$goal->from,$goal->to,$goal->amount)['percentage'], Utility::getValByName('decimal_number'), '.', '')); ?>%</h6>
                                                            <div class="progress mb-0">
                                                                <?php if($per<=33): ?>
                                                                    <div class="progress-bar bg-danger" style="width: <?php echo e($per); ?>%"></div>
                                                                <?php elseif($per>=33 && $per<=66): ?>
                                                                    <div class="progress-bar bg-warning" style="width: <?php echo e($per); ?>%"></div>
                                                                <?php else: ?>
                                                                    <div class="progress-bar bg-primary" style="width: <?php echo e($per); ?>%"></div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </span>
                                                    </span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="card pb-0">
                                    <div class="card-body text-center">
                                        <h6><?php echo e(__('There is no goal.')); ?></h6>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Settings Modal (Generic, place after all chart cards) -->
    <div class="modal fade" id="chartSettingsModal" tabindex="-1" aria-labelledby="chartSettingsModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="chartSettingsModalLabel"><?php echo e(__('Chart Settings')); ?></h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-4 mb-3">
                <label class="form-label"><?php echo e(__('Chart Type')); ?></label>
                <div class="d-flex flex-column gap-2">
                  <button type="button" class="btn btn-outline-primary chart-type-option" data-type="pie">Pie</button>
                  <button type="button" class="btn btn-outline-primary chart-type-option" data-type="bar">Bar</button>
                  <button type="button" class="btn btn-outline-primary chart-type-option" data-type="line">Line</button>
                  <button type="button" class="btn btn-outline-primary chart-type-option" data-type="doughnut">Donut</button>
                  <button type="button" class="btn btn-outline-primary chart-type-option" data-type="horizontalBar">Horizontal Bar</button>
                </div>
                <!-- Color Palette Section -->
                <div class="mt-4">
                  <label class="form-label"><?php echo e(__('Chart Color')); ?></label>
                  <div class="d-flex flex-wrap gap-2 color-palette mb-2">
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#0CAF60" style="width:32px;height:32px;background:#0CAF60;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#2e7d32" style="width:32px;height:32px;background:#2e7d32;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#ffc107" style="width:32px;height:32px;background:#ffc107;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#f44336" style="width:32px;height:32px;background:#f44336;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#584ED2" style="width:32px;height:32px;background:#584ED2;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#0C7785" style="width:32px;height:32px;background:#0C7785;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#48494B" style="width:32px;height:32px;background:#48494B;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#6fd944" style="width:32px;height:32px;background:#6fd944;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#1976d2" style="width:32px;height:32px;background:#1976d2;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#ff9800" style="width:32px;height:32px;background:#ff9800;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#B9406B" style="width:32px;height:32px;background:#B9406B;"></button>
                    <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#922C88" style="width:32px;height:32px;background:#922C88;"></button>
                  </div>
                  <button type="button" class="btn btn-outline-secondary btn-sm w-100 reset-color-btn mt-1"><?php echo e(__('Reset Color')); ?></button>
                </div>
              </div>
              <div class="col-md-8">
                <label class="form-label"><?php echo e(__('Live Preview')); ?></label>
                <div class="border rounded p-2 bg-light" style="min-height: 320px;">
                  <canvas id="chartPreviewCanvas" style="width:100%;height:300px;"></canvas>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
            <button type="button" class="btn btn-success" id="saveChartTypeBtn"><?php echo e(__('Save Changes')); ?></button>
          </div>
        </div>
      </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        if(window.innerWidth <= 500)
        {
            $('p').removeClass('text-sm');
        }
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    .color-palette .color-swatch {
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #ccc;
        transition: box-shadow 0.2s, border 0.2s;
        cursor: pointer;
    }
    .color-palette .color-swatch.selected {
        border: 2px solid #1976d2;
        box-shadow: 0 0 0 2px #1976d2;
    }
    @media (max-width: 767px) {
        .color-palette .color-swatch {
            width: 28px !important;
            height: 28px !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
// ... existing code ...
// Add color palette logic
const chartColorOptions = [
    '#0CAF60', '#2e7d32', '#ffc107', '#f44336', '#584ED2',
    '#0C7785', '#48494B', '#6fd944', '#1976d2', '#ff9800',
    '#B9406B', '#922C88'
];
let selectedColor = null;

function getStoredChartColor(chartKey) {
    return localStorage.getItem('chartColor_' + chartKey) || null;
}
function setStoredChartColor(chartKey, color) {
    localStorage.setItem('chartColor_' + chartKey, color);
}

function getChartColors(chartKey, baseColors) {
    const stored = getStoredChartColor(chartKey);
    if (stored) {
        // For single-color charts, use selected color; for multi, apply to all
        return baseColors.map(() => stored);
    }
    return baseColors;
}

// Update renderChartJs to use selected color
function renderChartJs(ctx, chartKey, type, colorOverride = null) {
    const dataSet = chartDataSets[chartKey];
    // For bar/line with two series (like incExpBarChart), handle as grouped
    let datasets = [{
        data: dataSet.data,
        backgroundColor: colorOverride ? dataSet.backgroundColor.map(() => colorOverride) : getChartColors(chartKey, dataSet.backgroundColor),
        label: chartKey === 'incExpBarChart' ? 'Income' : undefined
    }];
    if(chartKey === 'incExpBarChart' && Array.isArray(<?php echo json_encode($incExpBarChartData['expense'] ?? []); ?>) && <?php echo json_encode($incExpBarChartData['expense'] ?? []); ?>.length) {
        datasets = [
            {
                label: 'Income',
                data: dataSet.data,
                backgroundColor: colorOverride ? colorOverride : (dataSet.backgroundColor[0] || '#0CAF60')
            },
            {
                label: 'Expense',
                data: <?php echo json_encode($incExpBarChartData['expense'] ?? [1000, 1200, 900, 1400, 1300, 1250]); ?>,
                backgroundColor: dataSet.backgroundColor[1] || '#2279BD'
            }
        ];
    }
    return new Chart(ctx, {
        type: getChartJsType(type),
        data: {
            labels: dataSet.labels,
            datasets: datasets
        },
        options: getChartJsOptions(type)
    });
}

// Open modal with correct chart type and color
$(document).on('click', '.chart-settings-btn', function() {
    currentChartKey = $(this).data('chart');
    const savedType = getStoredChartType(currentChartKey);
    const savedColor = getStoredChartColor(currentChartKey);
    $('.chart-type-option').removeClass('active');
    $(`.chart-type-option[data-type="${savedType}"]`).addClass('active');
    // Color palette
    $('.color-swatch').removeClass('selected');
    if(savedColor) {
        $(`.color-swatch[data-color="${savedColor}"]`).addClass('selected');
        selectedColor = savedColor;
    } else {
        selectedColor = null;
    }
    // Render preview
    setTimeout(() => {
        if(previewChart) previewChart.destroy();
        const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
        previewChart = renderChartJs(ctx, currentChartKey, savedType, selectedColor);
    }, 200);
});
// Change preview on chart type select
$(document).on('click', '.chart-type-option', function() {
    $('.chart-type-option').removeClass('active');
    $(this).addClass('active');
    const type = $(this).data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type, selectedColor);
});
// Color palette click
$(document).on('click', '.color-swatch', function() {
    $('.color-swatch').removeClass('selected');
    $(this).addClass('selected');
    selectedColor = $(this).data('color');
    // Update preview
    const type = $('.chart-type-option.active').data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type, selectedColor);
});
// Reset color button
$(document).on('click', '.reset-color-btn', function() {
    $('.color-swatch').removeClass('selected');
    selectedColor = null;
    // Update preview to default color
    const type = $('.chart-type-option.active').data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type, null);
});
// Save and update main chart
$('#saveChartTypeBtn').on('click', function() {
    const selectedType = $('.chart-type-option.active').data('type');
    setStoredChartType(currentChartKey, selectedType);
    if(selectedColor) setStoredChartColor(currentChartKey, selectedColor);
    else localStorage.removeItem('chartColor_' + currentChartKey);
    // Update main chart
    if(mainCharts[currentChartKey]) mainCharts[currentChartKey].destroy();
    const ctx = document.getElementById(currentChartKey).getContext('2d');
    mainCharts[currentChartKey] = renderChartJs(ctx, currentChartKey, selectedType, selectedColor);
    $('#chartSettingsModal').modal('hide');
});
// Initialize all main charts on page load
function initAllMainCharts() {
    Object.keys(chartDataSets).forEach(chartKey => {
        const type = getStoredChartType(chartKey);
        const color = getStoredChartColor(chartKey);
        const ctx = document.getElementById(chartKey);
        if(ctx) {
            mainCharts[chartKey] = renderChartJs(ctx.getContext('2d'), chartKey, type, color);
        }
    });
}
// ... existing code ...
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/dashboard/account-dashboard.blade.php ENDPATH**/ ?>