{"__meta": {"id": "Xf9034ec0e4515219f92f61c778724c2c", "datetime": "2025-08-02 16:36:20", "utime": **********.144892, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152576.722046, "end": **********.144941, "duration": 3.4228951930999756, "duration_str": "3.42s", "measures": [{"label": "Booting", "start": 1754152576.722046, "relative_start": 0, "end": 1754152579.73034, "relative_end": 1754152579.73034, "duration": 3.008294105529785, "duration_str": "3.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152579.730366, "relative_start": 3.****************, "end": **********.144946, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1yrcQygPNaE9DJByctjoLMsTd1cevx7onb1l0lT6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1184177698 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1184177698\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1469182323 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1469182323\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1386644022 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1386644022\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-936663260 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936663260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1161277783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1161277783\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1615815896 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:36:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1Dc1FaRUFWbDdZeVJxSzNCK0R2TkE9PSIsInZhbHVlIjoid0d0ak85ZTdMUmpaYmFocFI5U2pPTFF4enhja1ZZZUJCeFY2YXRCeE9QVWFmS0ZXbnlPZ3FqTm9ScGwvR1cyWjduZnhZTEpVdUJ6TGNIVHQ5U0lOYmhNYkNZTVN4b0huS2VocnpHTUpod1JNdE0yenBCclZUR01UQWE0OEF4bWhpT29lTEdtcFNESmlpVlIzUE5ZTG1TVkFOK2tIdzVtZjNvUWZxc1YwV2NMbmNtNGQ5NHBoT3J0R2FmamkvQ3ZoanRUYkl4NWdRUTY2TE4xZFZYMzVvVzEwQ29MVVBtcEpsMTdBeVo3SFIvSWxGSHljK1FRYnZaMDROWU45cGJZcURvc1g0ZC9uWmwwb0lxeHIvKzZFd3RjSTEzS052aTAyLzJYK1pUMHR2dGJ5cjRIRTFobG4vRVNORlZ2MWhNR0hMemdac1F4cHZKYlBYRWQyY3Z0SkRWc2VVVHphVE5aWXZ3VmFraHR5Z251T01ScFdrYkZIYldVa09xanROczE3eTFpdHp4dkdZMDV2STFWZWZ3RytUZXBqTUJPUlVxTm9Bbit2TjNJNlV5c1JXRDFFOWJKQjA4L0UzbnRPUy8yRENidEEwNEdSR2lHdnJ1MkhQSXorenJZTDB3T2ljVjluUWJZdjhibTZiOXNxQVlORUxyWlBrTVVGQ1lnOUExWisiLCJtYWMiOiI3NzRkMWNlYWU5ZDdmZWRmZDRkMzQ1OTFmMzgzN2Q1ZWE2NWEwZTQ3ZDMwMjM1NTkzZDNlNThlMzIzY2I2MjgxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:36:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRLUW84UzJrSEhzYTdxaG0xbTlkaGc9PSIsInZhbHVlIjoiYm9WUUFRTEpqanRMdzhHSHcrS21CUW1sdXVjN1huQ2FXc1c1NU1tUVBlWlFoNGNVYTVhL3EybElSZVl6REFVQlpreHpsdktZUnJuL1VPVXZLclVGNUxvbUZ6NG5icldXdlR5L2xUNG5vRDJNYjNZcFhXQjZXN1N6MFpjbUVsR1RIbXpqbWxWOStLNDc1UzN2REFqa1hqb2MzKzd2UVlaNkVQMUVpcXFTYUxnQUxNZStiNjhWb2V0Y1VFZDI4eU9GT1JIZXU3cU85N09qUzJBVmY2NWlibnp4dGhjSys3SmdyNUw1TUdid0hRMnR4QVNSbHFKbWpWdnVqcFhMM2FWM2RVaDFheU4xTFRpdVBVVWJENkVOZ1hNQThYaUpTajZkbWlSbmdwK0pXdTlIVjhpODRKL0o5ZzhnbWdpZEZlaEJvUXVucFpMakR1cVFCUkFyL1dldEtpblA1TmswaDdsVFJma1ZkSUJMMm1jNUl3UUpoSEZMQnJKVVE1UjMvVFZZNkswWFowNUFWOUFJNjdLWDVNTzZzWThjNWd2TUh4RjcwVWFXVTlSTmNaTjJKNEhJcTdOZHRIZGRPQXJoOFAzamdyb25pYkhFVDllZFk5ZSs0SDBEdHRLNDA4S3pURVpQbW1TeHJoOEdMcngySjNHT1FkOFJqRk1ZeS9DRkpWMzAiLCJtYWMiOiI1MDNkNzU5YWMwNGJkZTI1MDU0Y2U2ZTkwZGExM2U5ZmEyMDYxMmEzZTMzZTIzYjEzZjljMWVmZTZhY2YwMWQwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:36:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1Dc1FaRUFWbDdZeVJxSzNCK0R2TkE9PSIsInZhbHVlIjoid0d0ak85ZTdMUmpaYmFocFI5U2pPTFF4enhja1ZZZUJCeFY2YXRCeE9QVWFmS0ZXbnlPZ3FqTm9ScGwvR1cyWjduZnhZTEpVdUJ6TGNIVHQ5U0lOYmhNYkNZTVN4b0huS2VocnpHTUpod1JNdE0yenBCclZUR01UQWE0OEF4bWhpT29lTEdtcFNESmlpVlIzUE5ZTG1TVkFOK2tIdzVtZjNvUWZxc1YwV2NMbmNtNGQ5NHBoT3J0R2FmamkvQ3ZoanRUYkl4NWdRUTY2TE4xZFZYMzVvVzEwQ29MVVBtcEpsMTdBeVo3SFIvSWxGSHljK1FRYnZaMDROWU45cGJZcURvc1g0ZC9uWmwwb0lxeHIvKzZFd3RjSTEzS052aTAyLzJYK1pUMHR2dGJ5cjRIRTFobG4vRVNORlZ2MWhNR0hMemdac1F4cHZKYlBYRWQyY3Z0SkRWc2VVVHphVE5aWXZ3VmFraHR5Z251T01ScFdrYkZIYldVa09xanROczE3eTFpdHp4dkdZMDV2STFWZWZ3RytUZXBqTUJPUlVxTm9Bbit2TjNJNlV5c1JXRDFFOWJKQjA4L0UzbnRPUy8yRENidEEwNEdSR2lHdnJ1MkhQSXorenJZTDB3T2ljVjluUWJZdjhibTZiOXNxQVlORUxyWlBrTVVGQ1lnOUExWisiLCJtYWMiOiI3NzRkMWNlYWU5ZDdmZWRmZDRkMzQ1OTFmMzgzN2Q1ZWE2NWEwZTQ3ZDMwMjM1NTkzZDNlNThlMzIzY2I2MjgxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:36:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRLUW84UzJrSEhzYTdxaG0xbTlkaGc9PSIsInZhbHVlIjoiYm9WUUFRTEpqanRMdzhHSHcrS21CUW1sdXVjN1huQ2FXc1c1NU1tUVBlWlFoNGNVYTVhL3EybElSZVl6REFVQlpreHpsdktZUnJuL1VPVXZLclVGNUxvbUZ6NG5icldXdlR5L2xUNG5vRDJNYjNZcFhXQjZXN1N6MFpjbUVsR1RIbXpqbWxWOStLNDc1UzN2REFqa1hqb2MzKzd2UVlaNkVQMUVpcXFTYUxnQUxNZStiNjhWb2V0Y1VFZDI4eU9GT1JIZXU3cU85N09qUzJBVmY2NWlibnp4dGhjSys3SmdyNUw1TUdid0hRMnR4QVNSbHFKbWpWdnVqcFhMM2FWM2RVaDFheU4xTFRpdVBVVWJENkVOZ1hNQThYaUpTajZkbWlSbmdwK0pXdTlIVjhpODRKL0o5ZzhnbWdpZEZlaEJvUXVucFpMakR1cVFCUkFyL1dldEtpblA1TmswaDdsVFJma1ZkSUJMMm1jNUl3UUpoSEZMQnJKVVE1UjMvVFZZNkswWFowNUFWOUFJNjdLWDVNTzZzWThjNWd2TUh4RjcwVWFXVTlSTmNaTjJKNEhJcTdOZHRIZGRPQXJoOFAzamdyb25pYkhFVDllZFk5ZSs0SDBEdHRLNDA4S3pURVpQbW1TeHJoOEdMcngySjNHT1FkOFJqRk1ZeS9DRkpWMzAiLCJtYWMiOiI1MDNkNzU5YWMwNGJkZTI1MDU0Y2U2ZTkwZGExM2U5ZmEyMDYxMmEzZTMzZTIzYjEzZjljMWVmZTZhY2YwMWQwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:36:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615815896\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-167842780 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1yrcQygPNaE9DJByctjoLMsTd1cevx7onb1l0lT6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167842780\", {\"maxDepth\":0})</script>\n"}}