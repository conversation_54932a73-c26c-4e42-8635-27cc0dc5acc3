{"__meta": {"id": "X2ba60d781e9bf4eaa4b1507b9540b49d", "datetime": "2025-08-02 16:34:06", "utime": 1754152446.106215, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152427.132443, "end": 1754152446.106291, "duration": 18.97384810447693, "duration_str": "18.97s", "measures": [{"label": "Booting", "start": 1754152427.132443, "relative_start": 0, "end": 1754152434.473009, "relative_end": 1754152434.473009, "duration": 7.340566158294678, "duration_str": "7.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754152434.482203, "relative_start": 7.349760055541992, "end": 1754152446.106298, "relative_end": 6.9141387939453125e-06, "duration": 11.62409496307373, "duration_str": "11.62s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44356088, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=58\" onclick=\"\">app/Http/Controllers/DashboardController.php:58-75</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.****************, "accumulated_duration_str": "618ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5591578, "duration": 0.****************, "duration_str": "618ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UIXco14JgY2SPjxPhQjeydnazbUfHFjOkYhwk9Nh", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1330561690 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1330561690\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1526416625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1526416625\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1633488651 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"644 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633488651\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1489805499 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489805499\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-677717816 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:34:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJaQm1PSWpMKzYyc1EzT2pBZ0lpbXc9PSIsInZhbHVlIjoiSHNYN2dLWVlJU3VidFJoTTJmSjJ4L0VYa3hIbTdRL2pwQXk4Kytja0ttemM3Tm5va3YwT1QyNDFieCtKcmVOWWFCclozT1BhTWJVZWhDS3VHcnZGTWpUOFdNYVQxd2N2ZzQvRDdXY2c1Rlg0UlIvQjVKeUY4L1dwVDA5OVkzNDlKZTArd3JSbUd1Qk1BWUVUSVRXRmxIbVgyMGZxZVBUV280TWFPR3Bremd5UTVQaHNLT0NPRGhhUnZCZ0txUE9UY0VxTDhhK0lUL0poTkRDcCtQTnNOKzI0N1U1RU00d3JMTkdjcWJsbk5NcXloTjNQKzNZZ2FLVlNxT3pVTWZPMmNuZ3RHVmpHTHNmVklkK1VPNDVjUDBOWklPN2poTW92dngwZ0VWWWlaZlU3aEwzdnFOcWk4SkJaRTcwRWVzNm9SRnRobzZtcktHcWZodkRKcXBMa1FVYkhESjRvZzBIaVMyREI2VXNkeWFjVzBMdWZGUHhzYmZhODcwcGVwY2F4anFxY1liL1pyRUhjTGZZeWUvcGpoSmY5QU8zVmxWU01NRXI5ekJZYS9qVWZ5b3p0cGFtZzJtb3cvbVBZQVVxdlZVQXllUUJJdVhtOG9icy9HY3prYTJFMU52bXJZMG5xWUM5VlFObmJneVBYZFRPQ3VWaW5iU2xJRnAxUXlJQ1IiLCJtYWMiOiJmMWRmYTA5ODlmZjQ5N2JjMDJkYzkxZDRlM2FhZjJiMTk4NTU0ZDI1MzBmMWU1MzlhZTVhYTRlZTlkOTQyYTQxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:34:05 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InFabnZDcW5sODBrY0oya3NsZ1NXREE9PSIsInZhbHVlIjoiM3F3cG54c0RzeCsvRVY4K1FObU9tSlV4V2hRbTRiSHF6MlJqSG42OTUvU1RkSmtta1YxNXV2NVhkZzJBa055SlJMRTRFeXlNMUlBYTl6SW9UQ0doUG5UWnN4V2dKK1ZWRGlMQ0IwdFhrUXJBemVOL2xYTU81eVhVMTMzTUhOcDZqRC9oTE9xUWJBM0Nuckk3SHpUWG8zZjhxK0JkNmpCR1JYcGZNSGFIbkQvSStLTzdXeEgvTjBqSng3S3llU2hDdVB2MDk3ZlJSZTN5SVBnSEJ0OFdDbzl3TlNNd1hURy9OQkphc1VkSmdpbXo5MUk2eHJmdjM3TlV2ampjbGNYaHBQd3ZrdnBOeWRPeTRPQTFCeFE3SjFDWDQxa0w2ZFp6eVBLc0M2cENkOEk1SW5kdGpnWmFTbmVJMzlDZlFpWlYxWWFpQUlKWUdySEZ4MVVDdGJ1ZmdFUk55YWpPYzhxM01EQytzNHJtRVhzSCtVZnJHcnRSZWUvWkZpYlpBcVR3d1dWL0phb0NtUDJmQVJ5ZjRRSVpia3M5UTcybGNUYmhOYk5RbWhmQnpuOS9lNndYQ2RMRkplSUVaMUI1NXU2VUxDa1ZvQjhqVHpYeGVpSmQvVWU3WmgvSHZ4Z3hJMkZaMVkrUWtRcVNmTmZvdkQ1SkRrQ1BBYWd0UWM5QUdycmMiLCJtYWMiOiI3ZGZiOTBiN2EzNWE1MzQzNTJiYzYzZjNhZWNlMTdlMzQ4YTZiYWE4MDJiNmFiYzA0NjNhNDJlZWFjNzZiYmFjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:34:05 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJaQm1PSWpMKzYyc1EzT2pBZ0lpbXc9PSIsInZhbHVlIjoiSHNYN2dLWVlJU3VidFJoTTJmSjJ4L0VYa3hIbTdRL2pwQXk4Kytja0ttemM3Tm5va3YwT1QyNDFieCtKcmVOWWFCclozT1BhTWJVZWhDS3VHcnZGTWpUOFdNYVQxd2N2ZzQvRDdXY2c1Rlg0UlIvQjVKeUY4L1dwVDA5OVkzNDlKZTArd3JSbUd1Qk1BWUVUSVRXRmxIbVgyMGZxZVBUV280TWFPR3Bremd5UTVQaHNLT0NPRGhhUnZCZ0txUE9UY0VxTDhhK0lUL0poTkRDcCtQTnNOKzI0N1U1RU00d3JMTkdjcWJsbk5NcXloTjNQKzNZZ2FLVlNxT3pVTWZPMmNuZ3RHVmpHTHNmVklkK1VPNDVjUDBOWklPN2poTW92dngwZ0VWWWlaZlU3aEwzdnFOcWk4SkJaRTcwRWVzNm9SRnRobzZtcktHcWZodkRKcXBMa1FVYkhESjRvZzBIaVMyREI2VXNkeWFjVzBMdWZGUHhzYmZhODcwcGVwY2F4anFxY1liL1pyRUhjTGZZeWUvcGpoSmY5QU8zVmxWU01NRXI5ekJZYS9qVWZ5b3p0cGFtZzJtb3cvbVBZQVVxdlZVQXllUUJJdVhtOG9icy9HY3prYTJFMU52bXJZMG5xWUM5VlFObmJneVBYZFRPQ3VWaW5iU2xJRnAxUXlJQ1IiLCJtYWMiOiJmMWRmYTA5ODlmZjQ5N2JjMDJkYzkxZDRlM2FhZjJiMTk4NTU0ZDI1MzBmMWU1MzlhZTVhYTRlZTlkOTQyYTQxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:34:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InFabnZDcW5sODBrY0oya3NsZ1NXREE9PSIsInZhbHVlIjoiM3F3cG54c0RzeCsvRVY4K1FObU9tSlV4V2hRbTRiSHF6MlJqSG42OTUvU1RkSmtta1YxNXV2NVhkZzJBa055SlJMRTRFeXlNMUlBYTl6SW9UQ0doUG5UWnN4V2dKK1ZWRGlMQ0IwdFhrUXJBemVOL2xYTU81eVhVMTMzTUhOcDZqRC9oTE9xUWJBM0Nuckk3SHpUWG8zZjhxK0JkNmpCR1JYcGZNSGFIbkQvSStLTzdXeEgvTjBqSng3S3llU2hDdVB2MDk3ZlJSZTN5SVBnSEJ0OFdDbzl3TlNNd1hURy9OQkphc1VkSmdpbXo5MUk2eHJmdjM3TlV2ampjbGNYaHBQd3ZrdnBOeWRPeTRPQTFCeFE3SjFDWDQxa0w2ZFp6eVBLc0M2cENkOEk1SW5kdGpnWmFTbmVJMzlDZlFpWlYxWWFpQUlKWUdySEZ4MVVDdGJ1ZmdFUk55YWpPYzhxM01EQytzNHJtRVhzSCtVZnJHcnRSZWUvWkZpYlpBcVR3d1dWL0phb0NtUDJmQVJ5ZjRRSVpia3M5UTcybGNUYmhOYk5RbWhmQnpuOS9lNndYQ2RMRkplSUVaMUI1NXU2VUxDa1ZvQjhqVHpYeGVpSmQvVWU3WmgvSHZ4Z3hJMkZaMVkrUWtRcVNmTmZvdkQ1SkRrQ1BBYWd0UWM5QUdycmMiLCJtYWMiOiI3ZGZiOTBiN2EzNWE1MzQzNTJiYzYzZjNhZWNlMTdlMzQ4YTZiYWE4MDJiNmFiYzA0NjNhNDJlZWFjNzZiYmFjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:34:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677717816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1853322951 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UIXco14JgY2SPjxPhQjeydnazbUfHFjOkYhwk9Nh</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853322951\", {\"maxDepth\":0})</script>\n"}}