{"__meta": {"id": "X50c28eabc492e8aea1414c1deedc7f23", "datetime": "2025-08-02 16:38:00", "utime": **********.654994, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:38:00] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.644107, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754152677.495104, "end": **********.655045, "duration": 3.1599409580230713, "duration_str": "3.16s", "measures": [{"label": "Booting", "start": 1754152677.495104, "relative_start": 0, "end": **********.3021, "relative_end": **********.3021, "duration": 2.8069958686828613, "duration_str": "2.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302153, "relative_start": 2.807049036026001, "end": **********.655051, "relative_end": 5.9604644775390625e-06, "duration": 0.35289788246154785, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50731408, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.604737, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.046340000000000006, "accumulated_duration_str": "46.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.428293, "duration": 0.00652, "duration_str": "6.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 14.07}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.477424, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 14.07, "width_percent": 3.151}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.492823, "duration": 0.03518, "duration_str": "35.18ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 17.221, "width_percent": 75.917}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.545757, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 93.138, "width_percent": 6.862}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1283290441 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1283290441\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-290566466 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-290566466\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-921897910 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921897910\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjlkMHlQYWM1NkYwTHQydTJTS0s5WUE9PSIsInZhbHVlIjoiUnlRVysxMXRqNGRQZUhGRjFZOEZLb0J0cWhZbGZ4a2Fjb2Y2NDBaMkRiR2Jjek9qTmlsL0VlVXhaa3ZacVVWcU5oOTEvZFQrMzhvWCticTJrVy8rWFp2NWhXY25tWjJBR1hKeEl5RGY3Wk5Td25sWnE5R2tsOW9hU0FieXV2QjFGM3BHdUF6c0NSeW5PdkozMXhESUJCaUp0V3RHb25Ybmp3Z3ZidVNNZmpWOEs5U1RTYlFIOVVhcjhoaXkyc0xwb3lOakhubzVsU3NXL2R3QlhMdHFreS84OTgzbHJvMXRZRUhFMjNyQ0ZURUNaSytmMlNHcEVUandqRDF4SkoxMU1IaThHTmR3ZjhaMGRvOGg5TUIvRjRJOEtqMy8ra0IzZDBjR2VWVzBOVVN2aXA4ZmdkbnBZOHNOWk9wWTR1ek9hdGl4QjhlTyttbXA2MGhmR1VBNFJQMnBHWU5NeXdvcUgwTU5LV3JHeDhGMHFrMHJ2Mk03YUNrREpia080czRJaEU3d1JDQ3BiRGJKMWVEc3BNQ05ZNlB1ZlU3L09FTDdiTmt2bllNR1hBbU1vY1c4RTBoSThXbjVOY2ZNbVFEbk5HeG9sZUhsOTI5dWdLa1lCamp2K3ZOdnV4UmNGOW9Md2NiTnlLVnJDblI2S0llN3k3YVFsN2ZOdzIxNWJESXoiLCJtYWMiOiIzNDI3YTg0ODUyMDU2MGUwYWExMjc2MGQwNTJhMWFkM2JlYjYxZTZiZGQyNmZhYTg1ZGRjZWY4NjE5MDgyMDBmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkVMZ1Y1TlFXV2dHTHdZZmRDdFhWNXc9PSIsInZhbHVlIjoiT0ZpeHptZWlQSnZOUkJVell4NVZyVHdTc2djdzNtdHYrYVJGaTJWY2VucVlDS1pkQlZxQ1hCVU5ZYzdBc1AxWUV1UjBjaW1JZ2hZTTJ5NlIwTE16ZVRBUDhoV0J3YVFZbDRMMmk1RmVZU2RaLytsQjF4Q0ZvM3BBdkRwenIrRi9VR1kxZ1QvdVpiYWxSYy9TMzUyd2NTN3pOZVhDVlVmZTZGU3VvUVM5ak91RXdwbnExM2N4RjVxL2lSclVOYU9IVlFIV3E2L0c4WWxmZEw0ZTV6VEtvc2d5Wll0a0tDVVVHd280YjNtcnIzdFVTQ2k0djVVTFcrMzBFK3hsQmRRSjh5THJFQmNWcGhReUJMKzdLSG45KzVnUE02RGVwYzN0YTVySVdSZUZpbmRhRGpWSU5hV1ppODRidUtkUk1iRW81dG1PcUV0bnVpTDEwbmtQNE5mdm1JZ0kyUkZpb0ZnYU5VNEhiOUx0Z1RORkZVNXZtbnVOMWFNYkNvTXQrTDVWS2FPWXo5bHR3Zm5laU1WRzNqTE5udEV5QXZWNFlzSmFqWWxoTmo4b28xSng5Z2hISFgzUllHWFFwUUZTbUJHTzhWWWdseGhtQjAra25Pc3l3eXBOWXZGTXR2R2xXNjAvRDgrcEt3bkdIYjVRVkcvUnhsSm83RW55S0dnazZFeFkiLCJtYWMiOiI1MTEwMTNmZDI3YzM3MWQzYjNmYWMwODA4NDkwOGM5NmRlZjRiYzNjNjE1ZjM1YTMzOTM3MjNiNzU0YTUwMTBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qX0CnOObck67LLGXwnxE1rbA703TvCmAFmaCZvwn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-105005508 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdlN0RYWDVrWnNiZDRmTGV4TW9zdmc9PSIsInZhbHVlIjoiY2dycU1ZcGk0UTNvM0Vramp1RTkwZGJveXVOS29wZHUzNXphUVQ0YnQwM0J3RDJZOWE4NFU2K1djQkN5K05NdWlGS0d2cE5WRDFwVndhVVBDV0QrNUcyVVoxZ3RhaEtlamxyQXlHNnJxNG15S1Rid3N6V0dJMUZvMFRCODUyc2g5Uy9qUG54TlRlcUMrUFlDN3RMMkpDK2ZFYXVsbWN3ZURWWUhjWjZSd1FRV3h3UnhEa3c5VTFnOWxJcXB6Nll4RkRwcktKTk5zdDRKcGVSRENZQjR5RW1rWVZkVVJXOEZtZWYwT1NwMjJSKzl1cm9mSkduZ2paaG01VmN1R3ZYQldrU2VlcHdZUVhMMnMzVHJXMDUxYUxRRlVHcm5FTmw3MnNVMFNrcDQyY3VJNDBQc05RVDhEcWRJYTJkTGlyN0pyaXRGR2Y2TEZ5QU5pTjNoVjFtS1E1WEw1RTdrWjZsNHF2Ry8vS0tQWWxZU2M4VlNhbnYxOU5SM3l0cnpjam42dGF5SzNQUS9mZVIybmFNS2pFb2duZTU0eFMxcTJ0UktVOU4wb1N5eXpKQWduTzhKZUNKWldBbXF6RFhLeVpaa0ZVYStKVkVmTEovQ0gybUlld0g3SlQwWTFWdHdrQmx2SzY3dDBXeTRJd1hrSHJxQkNuTFI3bDBXRmJ0Ymg0TWYiLCJtYWMiOiI2OTc2MDIzMmMwNjExYTRmOTVhYzUzODBkODk0NzYxMTZmNjEyYTI5MjM1MTRjNDA3ODdhNDA3OThmMTQ2ZTMwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InVkd3lKOXNIcnJ3SjdrT2Q0VnBJTmc9PSIsInZhbHVlIjoiNXdTalo5ckpDeVBmWmlMRCtaZWlXY0luKzZCQlRDT2lGWjVBVU9WNU9MMVRRaWtpelBuc0VaRWRjOW1idnlWRUE5eFhUTklkK01UM1JWV01YRngzSWF5amFMdGhUZFgyempTMm1hUnA3OXZDTUVObWtXV1VwaDNoc3BDSUl5WVI2UHREVm1JNUZnY05sckVtL29VN0FYMkR3bWpqZGtOTmxxK0EwSTlGS0Uzb1B2Q3I2MnFCNXRONTE0b1FhSWVJZnRzNjd0UlpJTVM4dmd3eTYrYnBZZStBckhqK1NSQ2NkZkk4TXFWY1pXR1BubUN1Qmx6MUVHbDFVN3cvMXltU2hhWXhQSmpjcGNQcUl2dng4M2tHdCszbEkrdmVWdnVOd1lGdkZLa1VuS2lSSXZiQUNrUCtoSzJiL01PTCsxZTNIRXhPQXBreG1zK3M1V1daUmhDQVBtdHdOblZIK0tQRFovbjNPb1RxZHNSaS9rVnd0SGpEMk5NdXpYWWNiWU01cXIwUTFESk9qWGgySWJxRFlBMXNDcytuSWRsQVExdGQyalZ5azB6ZUZ2aHhuY0svQVFCUENocDRXVG44Vk1YRkFlV0JrNjhPdHJoeGtPQVo5M1dkNU1KVXNhc3hZZlpZNklNWklyaWYxTi9lN2NlZHZmOER3Sm1HbG43d1cxcTQiLCJtYWMiOiJiNzY3NWNlNTU3N2M5ZDk2ZmE5ZmQ0OWQ0ZTE1OGM5ZDU1OWVmNjkyYTY1ZjMwZjc3Y2Y2MmNkZTM5YTNiN2MxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdlN0RYWDVrWnNiZDRmTGV4TW9zdmc9PSIsInZhbHVlIjoiY2dycU1ZcGk0UTNvM0Vramp1RTkwZGJveXVOS29wZHUzNXphUVQ0YnQwM0J3RDJZOWE4NFU2K1djQkN5K05NdWlGS0d2cE5WRDFwVndhVVBDV0QrNUcyVVoxZ3RhaEtlamxyQXlHNnJxNG15S1Rid3N6V0dJMUZvMFRCODUyc2g5Uy9qUG54TlRlcUMrUFlDN3RMMkpDK2ZFYXVsbWN3ZURWWUhjWjZSd1FRV3h3UnhEa3c5VTFnOWxJcXB6Nll4RkRwcktKTk5zdDRKcGVSRENZQjR5RW1rWVZkVVJXOEZtZWYwT1NwMjJSKzl1cm9mSkduZ2paaG01VmN1R3ZYQldrU2VlcHdZUVhMMnMzVHJXMDUxYUxRRlVHcm5FTmw3MnNVMFNrcDQyY3VJNDBQc05RVDhEcWRJYTJkTGlyN0pyaXRGR2Y2TEZ5QU5pTjNoVjFtS1E1WEw1RTdrWjZsNHF2Ry8vS0tQWWxZU2M4VlNhbnYxOU5SM3l0cnpjam42dGF5SzNQUS9mZVIybmFNS2pFb2duZTU0eFMxcTJ0UktVOU4wb1N5eXpKQWduTzhKZUNKWldBbXF6RFhLeVpaa0ZVYStKVkVmTEovQ0gybUlld0g3SlQwWTFWdHdrQmx2SzY3dDBXeTRJd1hrSHJxQkNuTFI3bDBXRmJ0Ymg0TWYiLCJtYWMiOiI2OTc2MDIzMmMwNjExYTRmOTVhYzUzODBkODk0NzYxMTZmNjEyYTI5MjM1MTRjNDA3ODdhNDA3OThmMTQ2ZTMwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InVkd3lKOXNIcnJ3SjdrT2Q0VnBJTmc9PSIsInZhbHVlIjoiNXdTalo5ckpDeVBmWmlMRCtaZWlXY0luKzZCQlRDT2lGWjVBVU9WNU9MMVRRaWtpelBuc0VaRWRjOW1idnlWRUE5eFhUTklkK01UM1JWV01YRngzSWF5amFMdGhUZFgyempTMm1hUnA3OXZDTUVObWtXV1VwaDNoc3BDSUl5WVI2UHREVm1JNUZnY05sckVtL29VN0FYMkR3bWpqZGtOTmxxK0EwSTlGS0Uzb1B2Q3I2MnFCNXRONTE0b1FhSWVJZnRzNjd0UlpJTVM4dmd3eTYrYnBZZStBckhqK1NSQ2NkZkk4TXFWY1pXR1BubUN1Qmx6MUVHbDFVN3cvMXltU2hhWXhQSmpjcGNQcUl2dng4M2tHdCszbEkrdmVWdnVOd1lGdkZLa1VuS2lSSXZiQUNrUCtoSzJiL01PTCsxZTNIRXhPQXBreG1zK3M1V1daUmhDQVBtdHdOblZIK0tQRFovbjNPb1RxZHNSaS9rVnd0SGpEMk5NdXpYWWNiWU01cXIwUTFESk9qWGgySWJxRFlBMXNDcytuSWRsQVExdGQyalZ5azB6ZUZ2aHhuY0svQVFCUENocDRXVG44Vk1YRkFlV0JrNjhPdHJoeGtPQVo5M1dkNU1KVXNhc3hZZlpZNklNWklyaWYxTi9lN2NlZHZmOER3Sm1HbG43d1cxcTQiLCJtYWMiOiJiNzY3NWNlNTU3N2M5ZDk2ZmE5ZmQ0OWQ0ZTE1OGM5ZDU1OWVmNjkyYTY1ZjMwZjc3Y2Y2MmNkZTM5YTNiN2MxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105005508\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1613698574 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613698574\", {\"maxDepth\":0})</script>\n"}}