{"__meta": {"id": "X63f5c5e2efea587270cc526bc3159ca4", "datetime": "2025-08-02 16:38:04", "utime": **********.057735, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:38:04] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.048094, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754152680.701911, "end": **********.057819, "duration": 3.355907917022705, "duration_str": "3.36s", "measures": [{"label": "Booting", "start": 1754152680.701911, "relative_start": 0, "end": **********.67522, "relative_end": **********.67522, "duration": 2.97330904006958, "duration_str": "2.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.675256, "relative_start": 2.9733450412750244, "end": **********.057825, "relative_end": 6.198883056640625e-06, "duration": 0.3825690746307373, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46137408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03583, "accumulated_duration_str": "35.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.871156, "duration": 0.030670000000000003, "duration_str": "30.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.599}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9712481, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.599, "width_percent": 7.759}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9913719, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 93.358, "width_percent": 3.098}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.003727, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 96.455, "width_percent": 3.545}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1692327946 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1692327946\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1022906815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1022906815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1582679916 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582679916\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-795398701 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjlkMHlQYWM1NkYwTHQydTJTS0s5WUE9PSIsInZhbHVlIjoiUnlRVysxMXRqNGRQZUhGRjFZOEZLb0J0cWhZbGZ4a2Fjb2Y2NDBaMkRiR2Jjek9qTmlsL0VlVXhaa3ZacVVWcU5oOTEvZFQrMzhvWCticTJrVy8rWFp2NWhXY25tWjJBR1hKeEl5RGY3Wk5Td25sWnE5R2tsOW9hU0FieXV2QjFGM3BHdUF6c0NSeW5PdkozMXhESUJCaUp0V3RHb25Ybmp3Z3ZidVNNZmpWOEs5U1RTYlFIOVVhcjhoaXkyc0xwb3lOakhubzVsU3NXL2R3QlhMdHFreS84OTgzbHJvMXRZRUhFMjNyQ0ZURUNaSytmMlNHcEVUandqRDF4SkoxMU1IaThHTmR3ZjhaMGRvOGg5TUIvRjRJOEtqMy8ra0IzZDBjR2VWVzBOVVN2aXA4ZmdkbnBZOHNOWk9wWTR1ek9hdGl4QjhlTyttbXA2MGhmR1VBNFJQMnBHWU5NeXdvcUgwTU5LV3JHeDhGMHFrMHJ2Mk03YUNrREpia080czRJaEU3d1JDQ3BiRGJKMWVEc3BNQ05ZNlB1ZlU3L09FTDdiTmt2bllNR1hBbU1vY1c4RTBoSThXbjVOY2ZNbVFEbk5HeG9sZUhsOTI5dWdLa1lCamp2K3ZOdnV4UmNGOW9Md2NiTnlLVnJDblI2S0llN3k3YVFsN2ZOdzIxNWJESXoiLCJtYWMiOiIzNDI3YTg0ODUyMDU2MGUwYWExMjc2MGQwNTJhMWFkM2JlYjYxZTZiZGQyNmZhYTg1ZGRjZWY4NjE5MDgyMDBmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkVMZ1Y1TlFXV2dHTHdZZmRDdFhWNXc9PSIsInZhbHVlIjoiT0ZpeHptZWlQSnZOUkJVell4NVZyVHdTc2djdzNtdHYrYVJGaTJWY2VucVlDS1pkQlZxQ1hCVU5ZYzdBc1AxWUV1UjBjaW1JZ2hZTTJ5NlIwTE16ZVRBUDhoV0J3YVFZbDRMMmk1RmVZU2RaLytsQjF4Q0ZvM3BBdkRwenIrRi9VR1kxZ1QvdVpiYWxSYy9TMzUyd2NTN3pOZVhDVlVmZTZGU3VvUVM5ak91RXdwbnExM2N4RjVxL2lSclVOYU9IVlFIV3E2L0c4WWxmZEw0ZTV6VEtvc2d5Wll0a0tDVVVHd280YjNtcnIzdFVTQ2k0djVVTFcrMzBFK3hsQmRRSjh5THJFQmNWcGhReUJMKzdLSG45KzVnUE02RGVwYzN0YTVySVdSZUZpbmRhRGpWSU5hV1ppODRidUtkUk1iRW81dG1PcUV0bnVpTDEwbmtQNE5mdm1JZ0kyUkZpb0ZnYU5VNEhiOUx0Z1RORkZVNXZtbnVOMWFNYkNvTXQrTDVWS2FPWXo5bHR3Zm5laU1WRzNqTE5udEV5QXZWNFlzSmFqWWxoTmo4b28xSng5Z2hISFgzUllHWFFwUUZTbUJHTzhWWWdseGhtQjAra25Pc3l3eXBOWXZGTXR2R2xXNjAvRDgrcEt3bkdIYjVRVkcvUnhsSm83RW55S0dnazZFeFkiLCJtYWMiOiI1MTEwMTNmZDI3YzM3MWQzYjNmYWMwODA4NDkwOGM5NmRlZjRiYzNjNjE1ZjM1YTMzOTM3MjNiNzU0YTUwMTBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795398701\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-640289849 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qX0CnOObck67LLGXwnxE1rbA703TvCmAFmaCZvwn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-640289849\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-609261338 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:38:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpFcFYyR0pSYXdNSWJtZTEvTVJLY1E9PSIsInZhbHVlIjoiTnNkTzlTWFRiQlMzNmUySlJzbmlodDBFK0M5YmNFejNuczR4N3Y4VjJEWTR1bnhWb2s0S1RGWTQzakVZK2k4bFJpV3FSbUNzc1hVZGZyYVUreHp6alpqVUh4eVNwNFRqdTFsRWtrNzhDZnErWHRFUXljb1E1ZlZMcHpxa1lmK0xtZ242cnlKQUtnVDFVNDh2VkdaZHhzYXdxcmNqOXVjenVOZHY5UGV4eUVTS1N4MjRQczY2MnE5MStVbklEQVUyUUF6Nll5ZEJ4VkZONWtYRW1McjUyTGdOS01EVE1NZHZKRlZQZlBZb1htdUZGVGhNZHU0YnI2SXlRQVVpV1BnbnJCbUxoWGZ1OU53Q2pTeXdvRDlFcjJJK3dBcWpEdzNlTkxKbFg0bUM3RVlSelMwUmFtcVMwa202a0I1NjUzWFNHeHpZbnJRSWIySExIVkhjckhCQUczNGE4c2RKYmdEUWxQK1ZHUm9tRFh5SWZBemJvQVJheVVEaTg0WGFRengzc1pOa3BaTlRQc3V6d2w4Tk5VUi9hWmVrZnljNGVKc1h3eERiOTY5ekt0Vi9BRTVObUd3ZlF0WFowWW9lN1VWNERSTDZubVUwU05KdG5idXpwSityNXp4VytERnhZczNFTXkxTk1iaFFicEZGL0ZNTkUwVTE2d2RtOUs4SUVORUoiLCJtYWMiOiJiZWJkYWUzYWIzNTI1MjIwNDhlY2VlZjNjNDI0MmIwN2FkMTUzZjNjNWUwZDczNjU1MGI4NTZlYjI2MGJlNzkzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjU5YWZBVjlndjY1SjhjRlI4aGErTnc9PSIsInZhbHVlIjoiaEMwMVBzd2hsWjFwMTlHOGhSRERLdjZRNUd1Qkp2OTlIR3ErSXJNK2p4akpyZGlFNUlXWWZWa1hDRVVSRjhjVEdtZFFuNGp2aVlEWWNzRE1ZRDBUd3dFb01GQVZFdk5EWUc5ZDM4RWpKdmR0MGpMa1V3MldMa1NJcC9FQXQ1QU5iYnF6LzVMUVBBVWxNMU5HZ2NIY3B3Y1h3YUZVV3I3VmJNeWd0ZXROK0ZaZFVMci9SdmQ0amNMQUJUd0hUSjhiNElPLzFtajhqOGM5N3JxVjNMVXYzSm84N2NiUlloRGpOckszVVI1NWhoZFRFaThUUlNTWDdJKzNSV2NXOVI2d2lrenUrWWpnSUhVZjVGcjdXQjZMZFpUOUt1amRkY29VekpWZDlwZ1ViRWgxcDdjdGl2Q212K1I5ZldQZjZkaW1iU1BRNWVtZjgySkt1b01iMUtBR04vaHBiTnBBL3JBK1daeEpaM0pYQUs5U01qM2hzeEJzdUxlZUpuRUJxYXl1NHkzNUxVQ3FFQkxKUUU0QXNLUllEWXVpQ2NxVm9ETUxnN0VNeG1uTTBOS1dDbGh6NzN3ZnNNM2RDSnVMR3BmRU15QzhkaXphaWxQUStCUE5DTVpaeTI5WG5oUlROQWh6VnYvUlJrdzh5M0U0OUxqdHBHMmdWeXNkdVlrRmwrR1kiLCJtYWMiOiJhNjA4NGMzMmVlZWFmMmY3YzAzODc4ODFjYjQ4OTA3YjQzOTY1ZDU2ZjhhMjVkZTQ5ZDAwODVkN2EwMzI3YmVjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:38:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpFcFYyR0pSYXdNSWJtZTEvTVJLY1E9PSIsInZhbHVlIjoiTnNkTzlTWFRiQlMzNmUySlJzbmlodDBFK0M5YmNFejNuczR4N3Y4VjJEWTR1bnhWb2s0S1RGWTQzakVZK2k4bFJpV3FSbUNzc1hVZGZyYVUreHp6alpqVUh4eVNwNFRqdTFsRWtrNzhDZnErWHRFUXljb1E1ZlZMcHpxa1lmK0xtZ242cnlKQUtnVDFVNDh2VkdaZHhzYXdxcmNqOXVjenVOZHY5UGV4eUVTS1N4MjRQczY2MnE5MStVbklEQVUyUUF6Nll5ZEJ4VkZONWtYRW1McjUyTGdOS01EVE1NZHZKRlZQZlBZb1htdUZGVGhNZHU0YnI2SXlRQVVpV1BnbnJCbUxoWGZ1OU53Q2pTeXdvRDlFcjJJK3dBcWpEdzNlTkxKbFg0bUM3RVlSelMwUmFtcVMwa202a0I1NjUzWFNHeHpZbnJRSWIySExIVkhjckhCQUczNGE4c2RKYmdEUWxQK1ZHUm9tRFh5SWZBemJvQVJheVVEaTg0WGFRengzc1pOa3BaTlRQc3V6d2w4Tk5VUi9hWmVrZnljNGVKc1h3eERiOTY5ekt0Vi9BRTVObUd3ZlF0WFowWW9lN1VWNERSTDZubVUwU05KdG5idXpwSityNXp4VytERnhZczNFTXkxTk1iaFFicEZGL0ZNTkUwVTE2d2RtOUs4SUVORUoiLCJtYWMiOiJiZWJkYWUzYWIzNTI1MjIwNDhlY2VlZjNjNDI0MmIwN2FkMTUzZjNjNWUwZDczNjU1MGI4NTZlYjI2MGJlNzkzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjU5YWZBVjlndjY1SjhjRlI4aGErTnc9PSIsInZhbHVlIjoiaEMwMVBzd2hsWjFwMTlHOGhSRERLdjZRNUd1Qkp2OTlIR3ErSXJNK2p4akpyZGlFNUlXWWZWa1hDRVVSRjhjVEdtZFFuNGp2aVlEWWNzRE1ZRDBUd3dFb01GQVZFdk5EWUc5ZDM4RWpKdmR0MGpMa1V3MldMa1NJcC9FQXQ1QU5iYnF6LzVMUVBBVWxNMU5HZ2NIY3B3Y1h3YUZVV3I3VmJNeWd0ZXROK0ZaZFVMci9SdmQ0amNMQUJUd0hUSjhiNElPLzFtajhqOGM5N3JxVjNMVXYzSm84N2NiUlloRGpOckszVVI1NWhoZFRFaThUUlNTWDdJKzNSV2NXOVI2d2lrenUrWWpnSUhVZjVGcjdXQjZMZFpUOUt1amRkY29VekpWZDlwZ1ViRWgxcDdjdGl2Q212K1I5ZldQZjZkaW1iU1BRNWVtZjgySkt1b01iMUtBR04vaHBiTnBBL3JBK1daeEpaM0pYQUs5U01qM2hzeEJzdUxlZUpuRUJxYXl1NHkzNUxVQ3FFQkxKUUU0QXNLUllEWXVpQ2NxVm9ETUxnN0VNeG1uTTBOS1dDbGh6NzN3ZnNNM2RDSnVMR3BmRU15QzhkaXphaWxQUStCUE5DTVpaeTI5WG5oUlROQWh6VnYvUlJrdzh5M0U0OUxqdHBHMmdWeXNkdVlrRmwrR1kiLCJtYWMiOiJhNjA4NGMzMmVlZWFmMmY3YzAzODc4ODFjYjQ4OTA3YjQzOTY1ZDU2ZjhhMjVkZTQ5ZDAwODVkN2EwMzI3YmVjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:38:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609261338\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1334561470 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cE0GLiWDjdJ0wNhlVLc67m1R2wItJ2SE7jZpWWOD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1334561470\", {\"maxDepth\":0})</script>\n"}}