{"__meta": {"id": "X53e7edb59b8c430bf3c9e9ae9e1f8c79", "datetime": "2025-08-02 16:37:45", "utime": **********.205807, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754152662.530225, "end": **********.20585, "duration": 2.6756248474121094, "duration_str": "2.68s", "measures": [{"label": "Booting", "start": 1754152662.530225, "relative_start": 0, "end": **********.013525, "relative_end": **********.013525, "duration": 2.483299970626831, "duration_str": "2.48s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.013552, "relative_start": 2.****************, "end": **********.205854, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3088\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1937 to 1943\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1937\" onclick=\"\">routes/web.php:1937-1943</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "yuKVQwEMWH3JnMuHavPZkP9XVRqWSen5rp4werdQ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-379740488 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-379740488\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-68195222 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-68195222\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1585111184 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1585111184\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-783158047 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783158047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-774559787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-774559787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-121912215 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 16:37:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1hVVplcVNIZ0I4VUJhb3hJT2txNVE9PSIsInZhbHVlIjoiSXU5Ym0wREFVQkNpQW81bFBoRUlXZlI2SndnVldjTUdYLzMya0pUL3ozczU4ek9qaTJ2RGhmQWtHeU9DMm9NVGViTmdvbkRKOGJ5ZkpKWTFLbXNtS1EwdUdXM0tGT0k0OHBVRHdYM0ZFNVM1N0xCcDkxUHUxdHZ1bDhCSWN1d0VaaFU5U1dOQnNUeVZGdG5ncEhMcDhRU2o3SlkxL0lMOFZkYTFmV2ZlY0J0MnZqQmFqaThyblBTNVFqT1c3ZTI5ZUZOMXZpSGVmTE9KQmRvOGZndVFQRFRFK2p6RG80K1ZseUxMMEhuMDhPR1EwY2w2MmxMSGFIcDFwQ3F2VUlqZThXR091bFJXWjkyT1BtMHBnRnNrVGRHZTNDRzNuYk5RbGtWTkJraUJid2Z6NnY5QWh6aHk4OVo1VTVOY0F3QUxGdXMwVzFwcGdZTnRtOUNQZHpLbWYwY1ozK0JtTzd3VFdEbG44azgzemRDenBCVFV4bGlZSnliMmt5eEFicG93Um1PVGF6Y2J2ZUZ4UDJ1SXRkbFN0M1c2bWtTRUQranIzQkFlK3pVeG5lbnF0SXdvaExqMjg4QkJMaEg3U2cxV1I0UUd0UWlsb1pUTDlWZXcvc1YyZFhvYkZxZlRqVWYyOHQrdmpLbzI1cEtyL0Vpck9rNGtsRGlxVHFGVEpBZnUiLCJtYWMiOiJhZmI5YWJkZDJjMTUzOTVmYTYyZDkwZDU4ZTc4ZjBkZDM0OGQzNzVkNjhmMzVkYjA1YzBkNmZlY2MxMzFmY2Q1IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlBwb2JEY2h1YmtENEFtcllmQXM4ZlE9PSIsInZhbHVlIjoiQTdKNUE5MXV3enJkN2JLbTBNd2pSbElsZFFPRU5yQjZxTWRHQzE0OXdtQnBrMkRyNGhUSUljWGhuS2lJdXdXb2g3Q3loQUVzNmE0bnJrNGJ1YitlaERFOWFlT1lVdXBGemJRZ0dyb3ptc0QxaFpPOFg2RmhZZkRZV0pmMUhkZSt3RWc0NHhEZ3JUOVdzNkFnWlVkQzZEYjdLVERZbFVYZlMxcUJRQUpTQjdENE1wSEJHVEFyNnlxbWJvMG5SaVJtVitXSkpNdThFc1l5UjFJaEdHd2JoakRCNVNrU0dUYWFxSm9CWFVreGVXZXNaT0NydHByU0pweHNDRXhlTlZqdHdNcklCSnJzc2xNbFZuamNhTllmOUxxd3pNUkFyTDNIZUtPK01BeHZEVk5JaW5XZURVa2xOWHRINzhmZG9PTXZKUTgrMjhjL01UaWI5c3pDbG5IMUw3T2JRT2dkb2RvTkNyVnlIOHhwcEZtaUthZkMxaGgvS2FnR05jVWxEbVpQd0ZpeEJIRmlPUHRCajl2eHZrQjBuRXlacjBIOEZKS3l6QmNScDVGaG5sajJTL3dETzR6TjE2Qk40emhDeGt6cnN1ajIrTkl0R1hrbnl2SlhJdmxwd1VtYVN3STNWaDBPcUw2cDVSWmVsRDVaWDUyUU5OSHVENkQ2eWo4a0lSMEQiLCJtYWMiOiIyM2M4ODgzZGMwY2MwNDZkOWI4YjdhZDI1MWI5YjQxNTYwNTVmMGQ4NjQ3ODhkNDA5MGMwNjZiOTRhNTA0YzlkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 18:37:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1hVVplcVNIZ0I4VUJhb3hJT2txNVE9PSIsInZhbHVlIjoiSXU5Ym0wREFVQkNpQW81bFBoRUlXZlI2SndnVldjTUdYLzMya0pUL3ozczU4ek9qaTJ2RGhmQWtHeU9DMm9NVGViTmdvbkRKOGJ5ZkpKWTFLbXNtS1EwdUdXM0tGT0k0OHBVRHdYM0ZFNVM1N0xCcDkxUHUxdHZ1bDhCSWN1d0VaaFU5U1dOQnNUeVZGdG5ncEhMcDhRU2o3SlkxL0lMOFZkYTFmV2ZlY0J0MnZqQmFqaThyblBTNVFqT1c3ZTI5ZUZOMXZpSGVmTE9KQmRvOGZndVFQRFRFK2p6RG80K1ZseUxMMEhuMDhPR1EwY2w2MmxMSGFIcDFwQ3F2VUlqZThXR091bFJXWjkyT1BtMHBnRnNrVGRHZTNDRzNuYk5RbGtWTkJraUJid2Z6NnY5QWh6aHk4OVo1VTVOY0F3QUxGdXMwVzFwcGdZTnRtOUNQZHpLbWYwY1ozK0JtTzd3VFdEbG44azgzemRDenBCVFV4bGlZSnliMmt5eEFicG93Um1PVGF6Y2J2ZUZ4UDJ1SXRkbFN0M1c2bWtTRUQranIzQkFlK3pVeG5lbnF0SXdvaExqMjg4QkJMaEg3U2cxV1I0UUd0UWlsb1pUTDlWZXcvc1YyZFhvYkZxZlRqVWYyOHQrdmpLbzI1cEtyL0Vpck9rNGtsRGlxVHFGVEpBZnUiLCJtYWMiOiJhZmI5YWJkZDJjMTUzOTVmYTYyZDkwZDU4ZTc4ZjBkZDM0OGQzNzVkNjhmMzVkYjA1YzBkNmZlY2MxMzFmY2Q1IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlBwb2JEY2h1YmtENEFtcllmQXM4ZlE9PSIsInZhbHVlIjoiQTdKNUE5MXV3enJkN2JLbTBNd2pSbElsZFFPRU5yQjZxTWRHQzE0OXdtQnBrMkRyNGhUSUljWGhuS2lJdXdXb2g3Q3loQUVzNmE0bnJrNGJ1YitlaERFOWFlT1lVdXBGemJRZ0dyb3ptc0QxaFpPOFg2RmhZZkRZV0pmMUhkZSt3RWc0NHhEZ3JUOVdzNkFnWlVkQzZEYjdLVERZbFVYZlMxcUJRQUpTQjdENE1wSEJHVEFyNnlxbWJvMG5SaVJtVitXSkpNdThFc1l5UjFJaEdHd2JoakRCNVNrU0dUYWFxSm9CWFVreGVXZXNaT0NydHByU0pweHNDRXhlTlZqdHdNcklCSnJzc2xNbFZuamNhTllmOUxxd3pNUkFyTDNIZUtPK01BeHZEVk5JaW5XZURVa2xOWHRINzhmZG9PTXZKUTgrMjhjL01UaWI5c3pDbG5IMUw3T2JRT2dkb2RvTkNyVnlIOHhwcEZtaUthZkMxaGgvS2FnR05jVWxEbVpQd0ZpeEJIRmlPUHRCajl2eHZrQjBuRXlacjBIOEZKS3l6QmNScDVGaG5sajJTL3dETzR6TjE2Qk40emhDeGt6cnN1ajIrTkl0R1hrbnl2SlhJdmxwd1VtYVN3STNWaDBPcUw2cDVSWmVsRDVaWDUyUU5OSHVENkQ2eWo4a0lSMEQiLCJtYWMiOiIyM2M4ODgzZGMwY2MwNDZkOWI4YjdhZDI1MWI5YjQxNTYwNTVmMGQ4NjQ3ODhkNDA5MGMwNjZiOTRhNTA0YzlkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 18:37:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121912215\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1594177080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yuKVQwEMWH3JnMuHavPZkP9XVRqWSen5rp4werdQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594177080\", {\"maxDepth\":0})</script>\n"}}